import { ForaChatApp } from '../src/ForaChatApp';
import fs from 'fs';
import path from 'path';

describe('ForaChatApp File Logging', () => {
  const logFilePath = path.join(process.cwd(), 'server.log');
  let originalConsole: any;

  beforeEach(() => {
    // Store original console methods
    originalConsole = {
      log: console.log,
      error: console.error,
      warn: console.warn,
      info: console.info
    };

    // Clean up any existing log file
    if (fs.existsSync(logFilePath)) {
      fs.unlinkSync(logFilePath);
    }
  });

  afterEach(() => {
    // Restore original console methods
    console.log = originalConsole.log;
    console.error = originalConsole.error;
    console.warn = originalConsole.warn;
    console.info = originalConsole.info;

    // Clean up log file after test
    if (fs.existsSync(logFilePath)) {
      fs.unlinkSync(logFilePath);
    }
  });

  test('should create server.log file when in quiet mode', (done) => {
    const app = new ForaChatApp({ quietMode: true });

    // Trigger a log message
    console.log('Test log message');

    // Give it a moment for the file write
    setTimeout(() => {
      expect(fs.existsSync(logFilePath)).toBe(true);

      const logContent = fs.readFileSync(logFilePath, 'utf8');
      expect(logContent).toContain('Test log message');
      expect(logContent).toContain('[INFO]');
      done();
    }, 100);
  });

  test('should log different levels to file in quiet mode', (done) => {
    const app = new ForaChatApp({ quietMode: true });

    console.log('Info message');
    console.error('Error message');
    console.warn('Warning message');

    setTimeout(() => {
      const logContent = fs.readFileSync(logFilePath, 'utf8');
      expect(logContent).toContain('[INFO] Info message');
      expect(logContent).toContain('[ERROR] Error message');
      expect(logContent).toContain('[WARN] Warning message');
      done();
    }, 100);
  });
});
