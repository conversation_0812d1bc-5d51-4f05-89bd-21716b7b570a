import { NewChatService, NewChatRequest } from '../../src/core/NewChatService';
import { ConversationService } from '../../src/core/ConversationService';
import { ThemeAnalysisService } from '../../src/core/ThemeAnalysisService';
import { CharacterDecisionService } from '../../src/core/CharacterDecisionService';
import { MoodService } from '../../src/core/MoodService';
import { CharacterMoods } from '../../src/models/types';

// Mock the dependencies
jest.mock('../../src/core/ConversationService');
jest.mock('../../src/core/ThemeAnalysisService');
jest.mock('../../src/core/CharacterDecisionService');
jest.mock('../../src/core/MoodService');
jest.mock('../../src/utils/Logger');

const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockThemeAnalysisService = ThemeAnalysisService as jest.Mocked<typeof ThemeAnalysisService>;
const mockCharacterDecisionService = CharacterDecisionService as jest.Mocked<typeof CharacterDecisionService>;
const mockMoodService = MoodService as jest.Mocked<typeof MoodService>;

describe('NewChatService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('processUserMessage', () => {
    it('should handle general inquiry with cordial response from random character', async () => {
      const request: NewChatRequest = { text: 'What\'s the weather like today?' };
      
      // Mock conversation creation
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage.mockResolvedValue({
        id: 1,
        character: 'user',
        text: request.text,
        conversation_id: 1,
        created_at: new Date(),
        updated_at: new Date()
      });
      mockConversationService.updateCharacterMoods.mockResolvedValue();
      mockConversationService.updateConversationMetadata.mockResolvedValue();

      // Mock mood generation
      const mockMoods: CharacterMoods = { Fora: 'enthusiastic', Jan: 'analytical', Lou: 'contemplative' };
      mockMoodService.generateRandomMoods.mockReturnValue(mockMoods);

      // Mock theme analysis for general inquiry
      const mockThemeAnalysis = {
        theme: 'general inquiry',
        skills: [],
        specialists: [],
        isGeneralGreeting: false,
        isGeneralInquiry: true,
        shouldEngageCharacters: true
      };
      mockThemeAnalysisService.analyzeConversationTheme.mockResolvedValue(mockThemeAnalysis);

      // Mock character decision for the selected character
      const mockCharacterDecision = {
        shouldRespond: true,
        reasoning: 'Providing cordial response to general inquiry',
        response: {
          text: 'I don\'t have access to current weather, but hope you\'re having a good day! ☀️ Anything work-related I can help with?',
          delay: 3000
        }
      };
      mockCharacterDecisionService.getCharacterDecision.mockResolvedValue(mockCharacterDecision);

      const result = await NewChatService.processUserMessage(request);

      // Verify the result
      expect(result.conversationId).toBe(1);
      expect(result.theme).toBe('general inquiry');
      expect(result.skills).toEqual([]);
      expect(result.isGeneralInquiry).toBe(true);
      expect(result.characterDecisions).toHaveLength(3); // All three characters should be in the decision array
      
      // One character should be selected to respond
      const respondingCharacters = result.characterDecisions.filter(d => d.queued);
      expect(respondingCharacters).toHaveLength(1);
      expect(['Fora', 'Jan', 'Lou']).toContain(respondingCharacters[0].character);
      expect(respondingCharacters[0].reasoning).toBe('Providing cordial response to general inquiry');

      // Verify service calls
      expect(mockConversationService.createConversation).toHaveBeenCalled();
      expect(mockConversationService.addMessage).toHaveBeenCalledWith('user', request.text, 1);
      expect(mockThemeAnalysisService.analyzeConversationTheme).toHaveBeenCalledWith(request.text, undefined);
      expect(mockCharacterDecisionService.getCharacterDecision).toHaveBeenCalledWith({
        character: expect.stringMatching(/^(Fora|Jan|Lou)$/),
        userMessage: request.text,
        conversationId: 1,
        themeAnalysis: {
          theme: 'general inquiry',
          skills: [],
          specialists: [],
          isGeneralGreeting: false,
          isGeneralInquiry: true,
          shouldEngageCharacters: true
        },
        isFollowUp: false
      });
    });

    it('should handle general greeting with cordial response from random character', async () => {
      const request: NewChatRequest = { text: 'Hello there!' };
      
      // Mock conversation creation
      const mockConversation = { id: 2, created_at: new Date(), updated_at: new Date() };
      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage.mockResolvedValue({
        id: 2,
        character: 'user',
        text: request.text,
        conversation_id: 2,
        created_at: new Date(),
        updated_at: new Date()
      });
      mockConversationService.updateCharacterMoods.mockResolvedValue();
      mockConversationService.updateConversationMetadata.mockResolvedValue();

      // Mock mood generation
      const mockMoods: CharacterMoods = { Fora: 'supportive', Jan: 'focused', Lou: 'empathetic' };
      mockMoodService.generateRandomMoods.mockReturnValue(mockMoods);

      // Mock theme analysis for general greeting
      const mockThemeAnalysis = {
        theme: 'general greeting',
        skills: [],
        specialists: [],
        isGeneralGreeting: true,
        isGeneralInquiry: false,
        shouldEngageCharacters: true
      };
      mockThemeAnalysisService.analyzeConversationTheme.mockResolvedValue(mockThemeAnalysis);

      // Mock character decision for the selected character
      const mockCharacterDecision = {
        shouldRespond: true,
        reasoning: 'Providing greeting response',
        response: {
          text: 'Hey there! 👋 What\'s on your mind today?',
          delay: 2000
        }
      };
      mockCharacterDecisionService.getCharacterDecision.mockResolvedValue(mockCharacterDecision);

      const result = await NewChatService.processUserMessage(request);

      // Verify the result
      expect(result.conversationId).toBe(2);
      expect(result.theme).toBe('general greeting');
      expect(result.skills).toEqual([]);
      expect(result.isGeneralGreeting).toBe(true);
      expect(result.characterDecisions).toHaveLength(3); // All three characters should be in the decision array
      
      // One character should be selected to respond
      const respondingCharacters = result.characterDecisions.filter(d => d.queued);
      expect(respondingCharacters).toHaveLength(1);
      expect(['Fora', 'Jan', 'Lou']).toContain(respondingCharacters[0].character);
      expect(respondingCharacters[0].reasoning).toBe('Providing greeting response');
    });

    it('should handle regular workplace topics normally', async () => {
      const request: NewChatRequest = { text: 'How can I improve my communication skills?' };
      
      // Mock conversation creation
      const mockConversation = { id: 3, created_at: new Date(), updated_at: new Date() };
      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage.mockResolvedValue({
        id: 3,
        character: 'user',
        text: request.text,
        conversation_id: 3,
        created_at: new Date(),
        updated_at: new Date()
      });
      mockConversationService.updateCharacterMoods.mockResolvedValue();
      mockConversationService.updateConversationMetadata.mockResolvedValue();

      // Mock mood generation
      const mockMoods: CharacterMoods = { Fora: 'determined', Jan: 'pragmatic', Lou: 'intuitive' };
      mockMoodService.generateRandomMoods.mockReturnValue(mockMoods);

      // Mock theme analysis for workplace topic
      const mockThemeAnalysis = {
        theme: 'communication skills',
        skills: ['Verbal Communication', 'Active Listening'],
        specialists: [],
        isGeneralGreeting: false,
        isGeneralInquiry: false,
        shouldEngageCharacters: true
      };
      mockThemeAnalysisService.analyzeConversationTheme.mockResolvedValue(mockThemeAnalysis);

      // Mock character decisions for all characters
      const mockCharacterDecisions = [
        { character: 'Fora', queued: true, reasoning: 'Leadership perspective on communication' },
        { character: 'Jan', queued: true, reasoning: 'Practical communication strategies' },
        { character: 'Lou', queued: false, reasoning: 'Others have covered the main points' }
      ];
      mockCharacterDecisionService.processAllCharacterDecisions.mockResolvedValue(mockCharacterDecisions);

      const result = await NewChatService.processUserMessage(request);

      // Verify the result
      expect(result.conversationId).toBe(3);
      expect(result.theme).toBe('communication skills');
      expect(result.skills).toEqual(['Verbal Communication', 'Active Listening']);
      expect(result.isGeneralGreeting).toBeUndefined();
      expect(result.isGeneralInquiry).toBeUndefined();
      expect(result.characterDecisions).toEqual(mockCharacterDecisions);

      // Verify service calls
      expect(mockCharacterDecisionService.processAllCharacterDecisions).toHaveBeenCalledWith(
        request.text,
        3,
        mockThemeAnalysis
      );
    });
  });
});
