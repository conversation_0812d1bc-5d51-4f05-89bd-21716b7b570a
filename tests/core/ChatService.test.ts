import { ChatService } from '../../src/core/ChatService';
import { ConversationService } from '../../src/core/ConversationService';
import { PromptService } from '../../src/core/PromptService';
import { LLMService } from '../../src/services/LLMService';
import { getMockResponse } from '../mocks/llmResponses';

// Mock the dependencies
jest.mock('../../src/core/ConversationService');
jest.mock('../../src/core/PromptService');

const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockPromptService = PromptService as jest.Mocked<typeof PromptService>;

// Create a mock LLM service
class MockLLMService extends LLMService {
  async generate(systemPrompt: string, userPrompt: string): Promise<any> {
    return getMockResponse(userPrompt);
  }

  async generateThemeAnalysis(systemPrompt: string, userPrompt: string): Promise<any> {
    return {
      theme: 'communication skills',
      skills: ['Verbal Communication'],
      reasoning: 'Mock theme analysis',
      specialists: []
    };
  }

  async generateJSON(systemPrompt: string, userPrompt: string): Promise<any> {
    return {
      shouldRespond: true,
      reasoning: 'Mock character decision',
      response: {
        text: 'Mock response',
        delay: 3000
      }
    };
  }
}

describe('ChatService', () => {
  let chatService: ChatService;
  let mockLLMService: MockLLMService;

  beforeEach(() => {
    jest.clearAllMocks();
    mockLLMService = new MockLLMService();
    chatService = new ChatService(mockLLMService);

    // Setup default mocks
    mockPromptService.getSystemPrompt.mockResolvedValue('Mock system prompt');
  });

  describe('processUserMessage', () => {
    it('should process a communication skills query', async () => {
      const userRequest = { text: 'How can I communicate better with my team?' };
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = { 
        id: 1, 
        character: 'user', 
        text: userRequest.text, 
        conversation_id: 1, 
        created_at: new Date(), 
        updated_at: new Date() 
      };
      const mockResponseMessage = { 
        id: 2, 
        character: 'Fora', 
        text: 'Hey! 👋 Sounds like you need some communication tips!', 
        conversation_id: 1, 
        created_at: new Date(), 
        updated_at: new Date() 
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      const result = await chatService.processUserMessage(userRequest);

      expect(result.response.theme).toBe('Effective Communication');
      expect(result.response.skills).toContain('clear communication');
      expect(result.response.reply).toHaveLength(4);
      expect(result.conversationId).toBe(1);
      expect(result.messageId).toBe(2);

      // Verify service calls
      expect(mockConversationService.createConversation).toHaveBeenCalled();
      expect(mockConversationService.addMessage).toHaveBeenCalledTimes(2);
      expect(mockPromptService.getSystemPrompt).toHaveBeenCalled();
    });

    it('should process a conflict resolution query', async () => {
      const userRequest = { text: 'There is conflict in my team' };
      const mockConversation = { id: 2, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = { 
        id: 3, 
        character: 'user', 
        text: userRequest.text, 
        conversation_id: 2, 
        created_at: new Date(), 
        updated_at: new Date() 
      };
      const mockResponseMessage = { 
        id: 4, 
        character: 'Jan', 
        text: 'Conflict at work? Time for some structured problem-solving 🎯', 
        conversation_id: 2, 
        created_at: new Date(), 
        updated_at: new Date() 
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      const result = await chatService.processUserMessage(userRequest);

      expect(result.response.theme).toBe('Workplace Conflict Resolution');
      expect(result.response.skills).toContain('conflict resolution');
      expect(result.conversationId).toBe(2);
    });

    it('should handle invalid LLM responses', async () => {
      const userRequest = { text: 'Test message' };
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      const mockUserMessage = { 
        id: 1, 
        character: 'user', 
        text: userRequest.text, 
        conversation_id: 1, 
        created_at: new Date(), 
        updated_at: new Date() 
      };

      mockConversationService.createConversation.mockResolvedValue(mockConversation);
      mockConversationService.addMessage.mockResolvedValueOnce(mockUserMessage);

      // Mock invalid LLM response
      jest.spyOn(mockLLMService, 'generate').mockResolvedValue({ invalid: 'response' });

      await expect(chatService.processUserMessage(userRequest))
        .rejects.toThrow('Invalid response from LLM - missing or empty reply array');
    });
  });

  describe('continueConversation', () => {
    it('should continue an existing conversation', async () => {
      const conversationId = 1;
      const userMessage = 'Can you give me more specific tips?';
      const mockConversation = { id: 1, created_at: new Date(), updated_at: new Date() };
      const mockMessages = [
        { id: 1, character: 'user', text: 'How can I communicate better?', conversation_id: 1, created_at: new Date(), updated_at: new Date() },
        { id: 2, character: 'Fora', text: 'Great question!', conversation_id: 1, created_at: new Date(), updated_at: new Date() },
      ];
      const mockUserMessage = { 
        id: 3, 
        character: 'user', 
        text: userMessage, 
        conversation_id: 1, 
        created_at: new Date(), 
        updated_at: new Date() 
      };
      const mockResponseMessage = { 
        id: 4, 
        character: 'Jan', 
        text: 'Clear communication = clear results.', 
        conversation_id: 1, 
        created_at: new Date(), 
        updated_at: new Date() 
      };

      mockConversationService.getConversation.mockResolvedValue(mockConversation);
      mockConversationService.getConversationMessages.mockResolvedValue(mockMessages);
      mockConversationService.addMessage
        .mockResolvedValueOnce(mockUserMessage)
        .mockResolvedValueOnce(mockResponseMessage);

      const result = await chatService.continueConversation(conversationId, userMessage);

      expect(result.conversationId).toBe(conversationId);
      expect(result.messageId).toBe(4);
      expect(mockConversationService.getConversation).toHaveBeenCalledWith(conversationId);
      expect(mockConversationService.getConversationMessages).toHaveBeenCalledWith(conversationId);
    });

    it('should throw error for non-existent conversation', async () => {
      const conversationId = 999;
      const userMessage = 'Test message';

      mockConversationService.getConversation.mockResolvedValue(null);

      await expect(chatService.continueConversation(conversationId, userMessage))
        .rejects.toThrow('Conversation 999 not found');
    });
  });
});
