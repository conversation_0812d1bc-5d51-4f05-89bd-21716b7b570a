import { CharacterDecisionService } from '../../src/core/CharacterDecisionService';
import { ThemeAnalysisResult } from '../../src/core/ThemeAnalysisService';
import { PromptService } from '../../src/core/PromptService';
import { ConversationService } from '../../src/core/ConversationService';
import { MoodService } from '../../src/core/MoodService';

// Mock dependencies
jest.mock('../../src/core/PromptService');
jest.mock('../../src/core/ConversationService');
jest.mock('../../src/core/MoodService');
jest.mock('../../src/services/GeminiLLMService');

const mockPromptService = PromptService as jest.Mocked<typeof PromptService>;
const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockMoodService = MoodService as jest.Mocked<typeof MoodService>;

describe('Specialist Injection', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('formatSpecialistContext', () => {
    test('should format specialist context correctly', () => {
      // Access the private method through reflection for testing
      const formatSpecialistContext = (CharacterDecisionService as any).formatSpecialistContext;
      
      const specialists = ['des', 'nat'];
      const result = formatSpecialistContext(specialists);
      
      expect(result).toContain('des, nat');
      expect(result).toContain('CURRENT CONVERSATION SPECIALISTS');
      expect(result).toContain('refer to these specialists as your friends');
    });

    test('should return empty string for no specialists', () => {
      const formatSpecialistContext = (CharacterDecisionService as any).formatSpecialistContext;
      
      const result = formatSpecialistContext([]);
      
      expect(result).toBe('');
    });
  });

  describe('getCharacterDecision with specialists', () => {
    test('should inject specialist context into Fora system prompt', async () => {
      // Mock the system prompt
      const baseSystemPrompt = 'You are Fora, an AI workplace mentor.';
      mockPromptService.getSystemPrompt.mockResolvedValue(baseSystemPrompt);
      
      // Mock conversation with no moods
      mockConversationService.getConversation.mockResolvedValue({
        id: 1,
        created_at: new Date(),
        updated_at: new Date(),
        character_moods: undefined
      });

      // Mock LLM service
      const mockLLMService = {
        generateJSON: jest.fn().mockResolvedValue({
          shouldRespond: true,
          reasoning: 'Test reasoning',
          response: {
            text: 'My friend Des is great at conflict resolution!',
            delay: 3000
          }
        })
      };
      
      // Set the mock LLM service
      CharacterDecisionService.setLLMService(mockLLMService as any);

      const themeAnalysis: ThemeAnalysisResult = {
        theme: 'conflict resolution',
        skills: ['Conflict Resolution'],
        specialists: ['des'],
        isGeneralGreeting: false,
        isGeneralInquiry: false,
        shouldEngageCharacters: true
      };

      const input = {
        character: 'Fora',
        userMessage: 'My coworker keeps interrupting me',
        conversationId: 1,
        themeAnalysis
      };

      await CharacterDecisionService.getCharacterDecision(input);

      // Verify that the LLM was called with the enhanced system prompt
      expect(mockLLMService.generateJSON).toHaveBeenCalledWith(
        expect.stringContaining('CURRENT CONVERSATION SPECIALISTS'),
        expect.any(String)
      );
      
      // Verify the system prompt contains specialist context
      const systemPromptCall = mockLLMService.generateJSON.mock.calls[0][0];
      expect(systemPromptCall).toContain('des');
      expect(systemPromptCall).toContain('refer to these specialists as your friends');
    });

    test('should not inject specialist context for non-Fora characters', async () => {
      // Mock the system prompt
      const baseSystemPrompt = 'You are Jan, an AI workplace strategist.';
      mockPromptService.getSystemPrompt.mockResolvedValue(baseSystemPrompt);
      
      // Mock conversation with no moods
      mockConversationService.getConversation.mockResolvedValue({
        id: 1,
        created_at: new Date(),
        updated_at: new Date(),
        character_moods: undefined
      });

      // Mock LLM service
      const mockLLMService = {
        generateJSON: jest.fn().mockResolvedValue({
          shouldRespond: true,
          reasoning: 'Test reasoning',
          response: {
            text: 'Here is my strategic advice',
            delay: 3000
          }
        })
      };
      
      // Set the mock LLM service
      CharacterDecisionService.setLLMService(mockLLMService as any);

      const themeAnalysis: ThemeAnalysisResult = {
        theme: 'conflict resolution',
        skills: ['Conflict Resolution'],
        specialists: ['des'],
        isGeneralGreeting: false,
        isGeneralInquiry: false,
        shouldEngageCharacters: true
      };

      const input = {
        character: 'Jan',
        userMessage: 'My coworker keeps interrupting me',
        conversationId: 1,
        themeAnalysis
      };

      await CharacterDecisionService.getCharacterDecision(input);

      // Verify that the LLM was NOT called with specialist context for Jan
      const systemPromptCall = mockLLMService.generateJSON.mock.calls[0][0];
      expect(systemPromptCall).not.toContain('CURRENT CONVERSATION SPECIALISTS');
      expect(systemPromptCall).toBe(baseSystemPrompt); // Should be unchanged
    });

    test('should include specialists in decision prompt for all characters', async () => {
      // Mock the system prompt
      const baseSystemPrompt = 'You are Lou, an AI workplace dynamics reader.';
      mockPromptService.getSystemPrompt.mockResolvedValue(baseSystemPrompt);
      
      // Mock conversation with no moods
      mockConversationService.getConversation.mockResolvedValue({
        id: 1,
        created_at: new Date(),
        updated_at: new Date(),
        character_moods: undefined
      });

      // Mock LLM service
      const mockLLMService = {
        generateJSON: jest.fn().mockResolvedValue({
          shouldRespond: false,
          reasoning: 'Not my expertise'
        })
      };
      
      // Set the mock LLM service
      CharacterDecisionService.setLLMService(mockLLMService as any);

      const themeAnalysis: ThemeAnalysisResult = {
        theme: 'salary negotiation',
        skills: ['Negotiation'],
        specialists: ['nat'],
        isGeneralGreeting: false,
        isGeneralInquiry: false,
        shouldEngageCharacters: true
      };

      const input = {
        character: 'Lou',
        userMessage: 'How do I negotiate my salary?',
        conversationId: 1,
        themeAnalysis
      };

      await CharacterDecisionService.getCharacterDecision(input);

      // Verify that the decision prompt includes specialists
      const decisionPromptCall = mockLLMService.generateJSON.mock.calls[0][1];
      expect(decisionPromptCall).toContain('Relevant specialists: nat');
    });
  });
});
