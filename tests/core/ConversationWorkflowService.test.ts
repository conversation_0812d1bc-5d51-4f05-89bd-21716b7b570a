import { ConversationWorkflowService } from '../../src/core/ConversationWorkflowService';
import { ConversationService } from '../../src/core/ConversationService';
import { PromptService } from '../../src/core/PromptService';
import { LLMService } from '../../src/services/LLMService';
import { DBOS } from '@dbos-inc/dbos-sdk';

// Mock the dependencies
jest.mock('../../src/core/ConversationService');
jest.mock('../../src/core/PromptService');
jest.mock('../../src/core/MessageQueueService', () => ({
  MessageQueueService: {
    checkSimilarity: jest.fn().mockResolvedValue({ isDuplicate: false, isSimilar: false }),
  },
}));

describe('ConversationWorkflowService', () => {
  let mockLLMService: jest.Mocked<LLMService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Create mock LLM service
    mockLLMService = {
      generate: jest.fn(),
      generateThemeAnalysis: jest.fn().mockResolvedValue({
        theme: 'communication skills',
        skills: ['Verbal Communication', 'Active Listening'],
        reasoning: 'Mock theme analysis reasoning',
        specialists: []
      }),
      generateJSON: jest.fn().mockResolvedValue({
        shouldRespond: true,
        reasoning: 'Mock character decision reasoning',
        response: {
          text: 'Mock character response',
          delay: 3000
        }
      }),
    } as jest.Mocked<LLMService>;
    
    // Set the mock LLM service
    ConversationWorkflowService.setLLMService(mockLLMService);

    // Also set the mock LLM service for ThemeAnalysisService
    const { ThemeAnalysisService } = require('../../src/core/ThemeAnalysisService');
    ThemeAnalysisService.setLLMService(mockLLMService);

    // Also set the mock LLM service for CharacterDecisionService
    const { CharacterDecisionService } = require('../../src/core/CharacterDecisionService');
    CharacterDecisionService.setLLMService(mockLLMService);

    // Mock MessageQueueService for this test
    const { MessageQueueService } = require('../../src/core/MessageQueueService');
    MessageQueueService.enqueueMessage = jest.fn().mockResolvedValue({
      id: 1,
      conversation_id: 1,
      character: 'Fora',
      text: 'Mock message',
      delay: 3000,
      status: 'pending',
      created_at: new Date(),
      updated_at: new Date()
    });
  });

  describe('hasThemeChangedSignificantly', () => {
    it('should return false for identical themes', () => {
      const result = (ConversationWorkflowService as any).hasThemeChangedSignificantly(
        'communication', 
        'communication'
      );
      expect(result).toBe(false);
    });

    it('should return false for themes in the same category', () => {
      const result = (ConversationWorkflowService as any).hasThemeChangedSignificantly(
        'communication', 
        'active listening'
      );
      expect(result).toBe(false);
    });

    it('should return true for themes in different categories', () => {
      const result = (ConversationWorkflowService as any).hasThemeChangedSignificantly(
        'communication', 
        'team building'
      );
      expect(result).toBe(true);
    });

    it('should return false when either theme is empty', () => {
      const result1 = (ConversationWorkflowService as any).hasThemeChangedSignificantly('', 'communication');
      const result2 = (ConversationWorkflowService as any).hasThemeChangedSignificantly('communication', '');
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });
  });

  describe('haveSkillsChangedSignificantly', () => {
    it('should return false when there is skill overlap', () => {
      const currentSkills = ['communication', 'teamwork'];
      const suggestedSkills = ['communication', 'leadership'];
      
      const result = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(
        currentSkills, 
        suggestedSkills
      );
      expect(result).toBe(false);
    });

    it('should return true when there is no skill overlap', () => {
      const currentSkills = ['communication', 'teamwork'];
      const suggestedSkills = ['leadership', 'time management'];
      
      const result = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(
        currentSkills, 
        suggestedSkills
      );
      expect(result).toBe(true);
    });

    it('should return false when either skills list is empty', () => {
      const result1 = (ConversationWorkflowService as any).haveSkillsChangedSignificantly([], ['communication']);
      const result2 = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(['communication'], []);
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });

    it('should handle string skills format', () => {
      const currentSkills = '["communication", "teamwork"]';
      const suggestedSkills = ['communication', 'leadership'];
      
      const result = (ConversationWorkflowService as any).haveSkillsChangedSignificantly(
        currentSkills, 
        suggestedSkills
      );
      expect(result).toBe(false);
    });
  });

  describe('shouldStartNewConversation', () => {
    it('should return true when no current conversation exists', () => {
      const result = ConversationWorkflowService.shouldStartNewConversation(
        null, 
        'communication', 
        ['active listening']
      );
      expect(result).toBe(true);
    });

    it('should return false when theme and skills are similar', () => {
      const currentConversation = {
        theme: 'communication',
        skills: ['active listening', 'feedback']
      };
      
      const result = ConversationWorkflowService.shouldStartNewConversation(
        currentConversation, 
        'feedback', 
        ['active listening', 'difficult conversations']
      );
      expect(result).toBe(false);
    });

    it('should return true when theme changes significantly', () => {
      const currentConversation = {
        theme: 'communication',
        skills: ['active listening']
      };
      
      const result = ConversationWorkflowService.shouldStartNewConversation(
        currentConversation, 
        'team building', 
        ['collaboration']
      );
      expect(result).toBe(true);
    });
  });

  describe('areConversationsSimilar', () => {
    it('should return true for conversations with similar themes and skills', () => {
      const conv1 = { theme: 'communication', skills: ['active listening'] };
      const conv2 = { theme: 'feedback', skills: ['active listening', 'difficult conversations'] };
      
      const result = ConversationWorkflowService.areConversationsSimilar(conv1, conv2);
      expect(result).toBe(true);
    });

    it('should return false for conversations with different themes and skills', () => {
      const conv1 = { theme: 'communication', skills: ['active listening'] };
      const conv2 = { theme: 'team building', skills: ['collaboration'] };
      
      const result = ConversationWorkflowService.areConversationsSimilar(conv1, conv2);
      expect(result).toBe(false);
    });

    it('should return false when either conversation is null', () => {
      const conv1 = { theme: 'communication', skills: ['active listening'] };
      
      const result1 = ConversationWorkflowService.areConversationsSimilar(null, conv1);
      const result2 = ConversationWorkflowService.areConversationsSimilar(conv1, null);
      
      expect(result1).toBe(false);
      expect(result2).toBe(false);
    });
  });

  describe('determineConversationRelevance', () => {
    beforeEach(() => {
      // Mock the dependencies
      (ConversationWorkflowService as any).getConversationRelevanceContext = jest.fn();
      (PromptService.getSystemPrompt as jest.Mock).mockResolvedValue('system prompt');
    });

    it('should return false when theme changes significantly', async () => {
      const mockContext = {
        conversation: { theme: 'communication', skills: ['active listening'] },
        recentMessages: [],
        messageCount: 1
      };

      (ConversationWorkflowService as any).getConversationRelevanceContext.mockResolvedValue(mockContext);
      
      mockLLMService.generate.mockResolvedValue({
        reply: [],
        theme: 'team building',
        skills: ['collaboration'],
        isRelated: true,
        reasoning: 'Test reasoning',
        confidence: 80,
        keyFactors: ['test'],
        suggestedTheme: 'team building',
        suggestedSkills: ['collaboration']
      } as any);

      const result = await ConversationWorkflowService.determineConversationRelevance(
        'How do I build better teams?', 
        1
      );

      expect(result).toBe(false);
    });

    it('should return true when theme and skills are similar', async () => {
      const mockContext = {
        conversation: { theme: 'communication', skills: ['active listening'] },
        recentMessages: [],
        messageCount: 1
      };

      (ConversationWorkflowService as any).getConversationRelevanceContext.mockResolvedValue(mockContext);
      
      mockLLMService.generate.mockResolvedValue({
        reply: [],
        theme: 'feedback',
        skills: ['active listening', 'difficult conversations'],
        isRelated: true,
        reasoning: 'Test reasoning',
        confidence: 80,
        keyFactors: ['test'],
        suggestedTheme: 'feedback',
        suggestedSkills: ['active listening', 'difficult conversations']
      } as any);

      const result = await ConversationWorkflowService.determineConversationRelevance(
        'How do I give better feedback?', 
        1
      );

      expect(result).toBe(true);
    });

    it('should return false when LLM determines message is not related', async () => {
      const mockContext = {
        conversation: { theme: 'communication', skills: ['active listening'] },
        recentMessages: [],
        messageCount: 1
      };

      (ConversationWorkflowService as any).getConversationRelevanceContext.mockResolvedValue(mockContext);
      
      mockLLMService.generate.mockResolvedValue({
        reply: [],
        theme: 'time management',
        skills: ['productivity'],
        isRelated: false,
        reasoning: 'Completely different topic',
        confidence: 90,
        keyFactors: ['topic change'],
        suggestedTheme: 'time management',
        suggestedSkills: ['productivity']
      } as any);

      const result = await ConversationWorkflowService.determineConversationRelevance(
        'What time is it?', 
        1
      );

      expect(result).toBe(false);
    });
  });

  describe('chatWorkflow', () => {
    beforeEach(() => {
      // Mock the dependencies
      (ConversationService.createConversation as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.addMessage as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.getConversation as jest.Mock).mockResolvedValue({
        id: 1,
        theme: 'communication',
        skills: ['active listening', 'feedback']
      });
      (ConversationService.getConversationMessages as jest.Mock).mockResolvedValue([
        { character: 'user', text: 'Hello' },
        { character: 'Fora', text: 'Hi there!' },
        { character: 'user', text: 'How do I give feedback?' }
      ]);
      (ConversationWorkflowService as any).getMessageCount = jest.fn().mockResolvedValue(3);
      (PromptService.getSystemPrompt as jest.Mock).mockResolvedValue('system prompt');

      mockLLMService.generate.mockResolvedValue({
        reply: [{ character: 'Fora', text: 'Great question!', delay: 2000 }],
        theme: 'communication',
        skills: ['feedback']
      });
    });

    it.skip('should include theme, skills, and conversation history in context for existing conversation', async () => {
      const result = await ConversationWorkflowService.chatWorkflow('How do I improve?', 1);

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation theme: communication')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation skills: active listening, feedback')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Full conversation history:')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('user: Hello')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Fora: Hi there!')
      );
    });

    it.skip('should not include context for new conversation', async () => {
      const result = await ConversationWorkflowService.chatWorkflow('Hello', undefined);

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        'Hello'
      );
    });

    it.skip('should handle conversation with string skills format', async () => {
      (ConversationService.getConversation as jest.Mock).mockResolvedValue({
        id: 1,
        theme: 'communication',
        skills: '["active listening", "feedback"]'
      });

      const result = await ConversationWorkflowService.chatWorkflow('How do I improve?', 1);

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation skills: active listening, feedback')
      );
    });
  });

  describe('interruptedChatWorkflow', () => {
    beforeEach(() => {
      (ConversationService.createConversation as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.addMessage as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.getConversation as jest.Mock).mockResolvedValue({
        id: 1,
        theme: 'communication',
        skills: ['active listening', 'feedback']
      });
      (ConversationService.getConversationMessages as jest.Mock).mockResolvedValue([
        { character: 'user', text: 'Hello' },
        { character: 'Fora', text: 'Hi there!' }
      ]);
      (ConversationWorkflowService as any).getMessageCount = jest.fn().mockResolvedValue(2);
      (PromptService.getSystemPrompt as jest.Mock).mockResolvedValue('system prompt');

      mockLLMService.generate.mockResolvedValue({
        reply: [{ character: 'Fora', text: 'I understand!', delay: 1000 }],
        theme: 'communication',
        skills: ['feedback']
      });
    });

    it.skip('should include theme, skills, conversation history, and interrupted messages in context', async () => {
      const previousMessages = [
        { character: 'Jan', text: 'Let me think about this...' },
        { character: 'Lou', text: 'Actually, I have an idea!' }
      ];

      const result = await ConversationWorkflowService.interruptedChatWorkflow(
        'Wait, I have a question!',
        previousMessages,
        1
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation theme: communication')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Conversation skills: active listening, feedback')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Full conversation history:')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Messages being processed when interrupted:')
      );

      expect(mockLLMService.generate).toHaveBeenCalledWith(
        'system prompt',
        expect.stringContaining('Jan: Let me think about this...')
      );
    });
  });

  describe('General Greeting Handling', () => {
    beforeEach(() => {
      // Mock ConversationService methods
      (ConversationService.createConversation as jest.Mock).mockResolvedValue({ id: 1 });
      (ConversationService.addMessage as jest.Mock).mockResolvedValue({});
      (ConversationService.getConversation as jest.Mock).mockResolvedValue({
        id: 1,
        character_moods: JSON.stringify({ Fora: 'enthusiastic', Jan: 'analytical', Lou: 'contemplative' })
      });
      (ConversationService.getConversationMessages as jest.Mock).mockResolvedValue([]);

      // Mock PromptService
      (PromptService.getSystemPrompt as jest.Mock).mockResolvedValue('system prompt');
    });

    it('should generate immediate response for general greeting', async () => {
      // Mock theme analysis for general greeting detection
      mockLLMService.generateThemeAnalysis.mockResolvedValueOnce({
        theme: 'general greeting',
        skills: [],
        reasoning: 'Simple greeting without specific skill development needs',
        specialists: []
      });

      // Mock character decision for greeting response
      mockLLMService.generateJSON.mockResolvedValueOnce({
        shouldRespond: true,
        reasoning: 'Providing greeting response',
        response: {
          text: 'Hello! Welcome to our workplace skills chat! 👋',
          delay: 1000
        }
      });

      const result = await ConversationWorkflowService.chatWorkflow('Hello');

      expect(result.theme).toBe('general greeting'); // Theme should be preserved for general greetings
      expect(result.reply).toHaveLength(1);
      expect(result.reply[0].character).toMatch(/^(Fora|Jan|Lou)$/);
      expect(result.reply[0].text).toBeTruthy();
      expect(result.reply[0].delay).toBe(1000);

      // Should have called theme analysis and character decision
      expect(mockLLMService.generateThemeAnalysis).toHaveBeenCalledTimes(1);
      expect(mockLLMService.generateJSON).toHaveBeenCalledTimes(1);
    });

    it('should not generate character thoughts for general greetings', async () => {
      // Mock theme analysis for general greeting detection
      mockLLMService.generateThemeAnalysis.mockResolvedValueOnce({
        theme: 'general greeting',
        skills: [],
        reasoning: 'Simple greeting without specific skill development needs',
        specialists: []
      });

      // Mock character decision for greeting response
      mockLLMService.generateJSON.mockResolvedValueOnce({
        shouldRespond: true,
        reasoning: 'Providing greeting response',
        response: {
          text: 'Hi there! Ready to work on some interpersonal skills?',
          delay: 1000
        }
      });

      const result = await ConversationWorkflowService.chatWorkflow('Hi');

      // Character thoughts should be empty for general greetings
      expect(result.characterThoughts).toHaveLength(0);
    });

    it('should handle greeting generation failure gracefully', async () => {
      // Mock theme analysis for general greeting detection
      mockLLMService.generateThemeAnalysis.mockResolvedValueOnce({
        theme: 'general greeting',
        skills: [],
        reasoning: 'Simple greeting without specific skill development needs',
        specialists: []
      });

      // Mock character decision failure (character decides not to respond)
      mockLLMService.generateJSON.mockResolvedValueOnce({
        shouldRespond: false,
        reasoning: 'Unable to generate appropriate greeting response'
      });

      const result = await ConversationWorkflowService.chatWorkflow('Good morning');

      expect(result.theme).toBe('general greeting'); // Theme should be preserved
      expect(result.reply).toHaveLength(0); // Should fallback to empty reply when character doesn't respond
      expect(result.characterThoughts).toHaveLength(0);
    });

    it('should generate cordial response for general inquiry', async () => {
      // Mock theme analysis for general inquiry detection
      mockLLMService.generateThemeAnalysis.mockResolvedValueOnce({
        theme: 'general inquiry',
        skills: [],
        reasoning: 'Question unrelated to interpersonal professional skills',
        specialists: []
      });

      // Mock character decision for cordial response
      mockLLMService.generateJSON.mockResolvedValueOnce({
        shouldRespond: true,
        reasoning: 'Providing cordial response to general inquiry',
        response: {
          text: 'I don\'t have access to current weather, but hope you\'re having a good day! ☀️ Anything work-related I can help with?',
          delay: 3000
        }
      });

      const result = await ConversationWorkflowService.chatWorkflow('What is the weather like?');

      expect(result.theme).toBe('general inquiry'); // Theme should be preserved for general inquiries
      expect(result.reply).toHaveLength(1); // Should generate one cordial response
      expect(result.reply[0].character).toMatch(/^(Fora|Jan|Lou)$/); // Should be from one of the characters
      expect(result.characterThoughts).toHaveLength(0); // Should not generate character thoughts
    });

    it('should not generate character thoughts for general inquiry', async () => {
      const mockLLMResponse = {
        reply: [],
        theme: 'general inquiry',
        skills: []
      };

      const characterThoughts = ConversationWorkflowService.getCharacterThoughtInfo(
        { id: 1 },
        'What is the weather like?',
        mockLLMResponse
      );

      expect(characterThoughts).toHaveLength(0);
    });
  });
});
