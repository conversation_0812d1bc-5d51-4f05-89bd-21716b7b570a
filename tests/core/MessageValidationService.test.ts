import { MessageValidationService } from '../../src/core/MessageValidationService';
import { MessageQueueService } from '../../src/core/MessageQueueService';
import { ConversationService } from '../../src/core/ConversationService';
import { PromptService } from '../../src/core/PromptService';
import { GeminiLLMService } from '../../src/services/GeminiLLMService';
import { QueuedMessage, ValidationDecision } from '../../src/models/types';

// Mock dependencies
jest.mock('../../src/core/MessageQueueService', () => ({
  MessageQueueService: {
    getMessageById: jest.fn(),
    updateValidationStatus: jest.fn(),
    updateMessageStatus: jest.fn(),
    updateMessageText: jest.fn(),
  }
}));

jest.mock('../../src/core/ConversationService', () => ({
  ConversationService: {
    getConversationMessages: jest.fn(),
    getConversation: jest.fn(),
  }
}));

jest.mock('../../src/core/PromptService', () => ({
  PromptService: {
    getSystemPrompt: jest.fn(),
  }
}));

jest.mock('../../src/services/GeminiLLMService', () => ({
  GeminiLLMService: {
    generateJSONStatic: jest.fn(),
  }
}));

const mockMessageQueueService = MessageQueueService as jest.Mocked<typeof MessageQueueService>;
const mockConversationService = ConversationService as jest.Mocked<typeof ConversationService>;
const mockPromptService = PromptService as jest.Mocked<typeof PromptService>;
const mockGeminiLLMService = GeminiLLMService as jest.Mocked<typeof GeminiLLMService>;

describe('MessageValidationService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  const mockQueuedMessage: QueuedMessage = {
    id: 1,
    conversation_id: 1,
    character: 'Fora',
    text: 'This is a test message',
    delay_ms: 3000,
    status: 'PENDING',
    priority: 100,
    created_at: new Date(),
    updated_at: new Date(),
    validation_status: 'NOT_VALIDATED'
  };

  const mockConversationHistory = [
    {
      id: 1,
      conversation_id: 1,
      character: 'user',
      text: 'Hello everyone',
      created_at: new Date(),
      updated_at: new Date()
    },
    {
      id: 2,
      conversation_id: 1,
      character: 'Jan',
      text: 'Hi there, what can we help with?',
      created_at: new Date(),
      updated_at: new Date()
    }
  ];

  describe('validateQueuedMessage', () => {
    it('should validate a message and return SEND_AS_IS decision', async () => {
      // Setup mocks
      mockMessageQueueService.getMessageById.mockResolvedValue(mockQueuedMessage);
      mockMessageQueueService.updateValidationStatus.mockResolvedValue();
      mockConversationService.getConversationMessages.mockResolvedValue(mockConversationHistory);
      mockConversationService.getConversation.mockResolvedValue({
        id: 1,
        theme: 'general',
        skills: [],
        created_at: new Date(),
        updated_at: new Date()
      });
      mockPromptService.getSystemPrompt.mockResolvedValue('Test system prompt');
      mockGeminiLLMService.generateJSONStatic.mockResolvedValue({
        decision: 'SEND_AS_IS',
        reasoning: 'Message is still relevant'
      });

      // Execute
      const result = await MessageValidationService.validateQueuedMessage(1);

      // Verify
      expect(result.decision).toBe('SEND_AS_IS');
      expect(result.reasoning).toBe('Message is still relevant');
      expect(mockMessageQueueService.updateValidationStatus).toHaveBeenCalledWith(1, 'VALIDATING');
      expect(mockMessageQueueService.updateValidationStatus).toHaveBeenCalledWith(1, 'VALIDATED');
    });

    it('should handle WITHDRAW decision', async () => {
      // Setup mocks
      mockMessageQueueService.getMessageById.mockResolvedValue(mockQueuedMessage);
      mockMessageQueueService.updateValidationStatus.mockResolvedValue();
      mockMessageQueueService.updateMessageStatus.mockResolvedValue();
      mockConversationService.getConversationMessages.mockResolvedValue(mockConversationHistory);
      mockConversationService.getConversation.mockResolvedValue({
        id: 1,
        theme: 'general',
        skills: [],
        created_at: new Date(),
        updated_at: new Date()
      });
      mockPromptService.getSystemPrompt.mockResolvedValue('Test system prompt');
      mockGeminiLLMService.generateJSONStatic.mockResolvedValue({
        decision: 'WITHDRAW',
        reasoning: 'Message is no longer relevant'
      });

      // Execute
      const result = await MessageValidationService.validateQueuedMessage(1);

      // Verify
      expect(result.decision).toBe('WITHDRAW');
      expect(mockMessageQueueService.updateMessageStatus).toHaveBeenCalledWith(1, 'WITHDRAWN');
    });

    it('should handle REVISE decision', async () => {
      // Setup mocks
      mockMessageQueueService.getMessageById.mockResolvedValue(mockQueuedMessage);
      mockMessageQueueService.updateValidationStatus.mockResolvedValue();
      mockMessageQueueService.updateMessageText.mockResolvedValue();
      mockConversationService.getConversationMessages.mockResolvedValue(mockConversationHistory);
      mockConversationService.getConversation.mockResolvedValue({
        id: 1,
        theme: 'general',
        skills: [],
        created_at: new Date(),
        updated_at: new Date()
      });
      mockPromptService.getSystemPrompt.mockResolvedValue('Test system prompt');
      mockGeminiLLMService.generateJSONStatic.mockResolvedValue({
        decision: 'REVISE',
        reasoning: 'Message needs updating',
        revisedText: 'This is the revised message'
      });

      // Execute
      const result = await MessageValidationService.validateQueuedMessage(1);

      // Verify
      expect(result.decision).toBe('REVISE');
      expect(result.revisedText).toBe('This is the revised message');
      expect(mockMessageQueueService.updateMessageText).toHaveBeenCalledWith(1, 'This is the revised message');
    });

    it('should handle validation errors gracefully', async () => {
      // Setup mocks
      mockMessageQueueService.getMessageById.mockResolvedValue(mockQueuedMessage);
      mockMessageQueueService.updateValidationStatus.mockResolvedValue();
      mockConversationService.getConversationMessages.mockRejectedValue(new Error('Database error'));

      // Execute
      const result = await MessageValidationService.validateQueuedMessage(1);

      // Verify fallback behavior
      expect(result.decision).toBe('SEND_AS_IS');
      expect(result.reasoning).toBe('Validation failed, sending original message');
      expect(mockMessageQueueService.updateValidationStatus).toHaveBeenCalledWith(1, 'NOT_VALIDATED');
    });
  });

  describe('parseValidationResponse', () => {
    it('should parse valid response correctly', () => {
      const response = {
        decision: 'SEND_AS_IS' as ValidationDecision,
        reasoning: 'Test reasoning'
      };

      const result = MessageValidationService['parseValidationResponse'](response);

      expect(result.decision).toBe('SEND_AS_IS');
      expect(result.reasoning).toBe('Test reasoning');
    });

    it('should throw error for invalid decision', () => {
      const response = {
        decision: 'INVALID_DECISION',
        reasoning: 'Test reasoning'
      };

      expect(() => {
        MessageValidationService['parseValidationResponse'](response);
      }).toThrow('Invalid validation decision: INVALID_DECISION');
    });

    it('should throw error for missing revised text on REVISE', () => {
      const response = {
        decision: 'REVISE' as ValidationDecision,
        reasoning: 'Test reasoning'
      };

      expect(() => {
        MessageValidationService['parseValidationResponse'](response);
      }).toThrow('Missing revised text for REVISE decision');
    });
  });
});
