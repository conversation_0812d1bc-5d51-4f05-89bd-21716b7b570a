import { MoodService } from '../../src/core/MoodService';
import { CharacterMoods } from '../../src/models/types';

describe('MoodService', () => {
  describe('generateRandomMoods', () => {
    it('should generate moods for all three characters', () => {
      const moods = MoodService.generateRandomMoods();
      
      expect(moods).toHaveProperty('Fora');
      expect(moods).toHaveProperty('Jan');
      expect(moods).toHaveProperty('Lou');
    });

    it('should generate valid moods for each character', () => {
      const moods = MoodService.generateRandomMoods();
      
      // Fora moods
      const validForaMoods = ['enthusiastic', 'supportive', 'energetic', 'thoughtful', 'determined', 'collaborative', 'tired', 'grumpy', 'diengaged'];
      expect(validForaMoods).toContain(moods.Fora);

      // Jan moods
      const validJanMoods = ['analytical', 'focused', 'pragmatic', 'direct', 'methodical', 'decisive', 'procrastinating', 'distracted', 'overwhelmed'];
      expect(validJanMoods).toContain(moods.Jan);

      // Lou moods
      const validLouMoods = ['contemplative', 'intuitive', 'empathetic', 'observant', 'reflective', 'grounded', 'flighty', 'daydreaming', 'disconnected'];
      expect(validLouMoods).toContain(moods.Lou);
    });

    it('should generate different moods on multiple calls', () => {
      const moods1 = MoodService.generateRandomMoods();
      const moods2 = MoodService.generateRandomMoods();
      const moods3 = MoodService.generateRandomMoods();
      
      // At least one character should have different moods across multiple generations
      const allSame = (
        moods1.Fora === moods2.Fora && moods2.Fora === moods3.Fora &&
        moods1.Jan === moods2.Jan && moods2.Jan === moods3.Jan &&
        moods1.Lou === moods2.Lou && moods2.Lou === moods3.Lou
      );
      
      expect(allSame).toBe(false);
    });
  });

  describe('formatMoodsForPrompt', () => {
    it('should format moods correctly for prompt injection', () => {
      const moods: CharacterMoods = {
        Fora: 'enthusiastic',
        Jan: 'analytical',
        Lou: 'contemplative'
      };
      
      const formatted = MoodService.formatMoodsForPrompt(moods);
      
      expect(formatted).toContain('Character moods for this conversation:');
      expect(formatted).toContain('Fora is feeling enthusiastic');
      expect(formatted).toContain('Jan is feeling analytical');
      expect(formatted).toContain('Lou is feeling contemplative');
    });
  });

  describe('formatCharacterMoodForPrompt', () => {
    it('should format individual character mood correctly', () => {
      const moods: CharacterMoods = {
        Fora: 'supportive',
        Jan: 'focused',
        Lou: 'empathetic'
      };
      
      const foraFormatted = MoodService.formatCharacterMoodForPrompt('Fora', moods);
      const janFormatted = MoodService.formatCharacterMoodForPrompt('Jan', moods);
      const louFormatted = MoodService.formatCharacterMoodForPrompt('Lou', moods);
      
      expect(foraFormatted).toBe("You're in a supportive mood today.");
      expect(janFormatted).toBe("You're in a focused mood today.");
      expect(louFormatted).toBe("You're in a empathetic mood today.");
    });

    it('should return empty string for invalid character', () => {
      const moods: CharacterMoods = {
        Fora: 'enthusiastic',
        Jan: 'analytical',
        Lou: 'contemplative'
      };
      
      const result = MoodService.formatCharacterMoodForPrompt('InvalidCharacter', moods);
      expect(result).toBe('');
    });
  });

  describe('parseCharacterMoods', () => {
    it('should parse JSON string correctly', () => {
      const moodsJson = '{"Fora":"enthusiastic","Jan":"analytical","Lou":"contemplative"}';
      const parsed = MoodService.parseCharacterMoods(moodsJson);
      
      expect(parsed).toEqual({
        Fora: 'enthusiastic',
        Jan: 'analytical',
        Lou: 'contemplative'
      });
    });

    it('should return object as-is if already parsed', () => {
      const moodsObj: CharacterMoods = {
        Fora: 'supportive',
        Jan: 'focused',
        Lou: 'empathetic'
      };
      
      const result = MoodService.parseCharacterMoods(moodsObj);
      expect(result).toBe(moodsObj);
    });

    it('should return null for null/undefined input', () => {
      expect(MoodService.parseCharacterMoods(null)).toBe(null);
      expect(MoodService.parseCharacterMoods(undefined)).toBe(null);
    });

    it('should return null for invalid JSON string', () => {
      const invalidJson = '{"Fora":"enthusiastic","Jan":}';
      const result = MoodService.parseCharacterMoods(invalidJson);
      expect(result).toBe(null);
    });
  });
});
