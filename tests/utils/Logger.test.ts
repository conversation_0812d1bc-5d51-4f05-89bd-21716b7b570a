import fs from 'fs';
import path from 'path';
import { Logger, logger } from '../../src/utils/Logger';

// Mock DBOS to avoid dependencies in tests
jest.mock('@dbos-inc/dbos-sdk', () => ({
  DBOS: {
    logger: {
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn(),
      debug: jest.fn(),
    },
  },
}));

describe('Logger', () => {
  const testLogPath = path.join(process.cwd(), 'test-server.log');
  let testLogger: Logger;

  beforeEach(() => {
    // Clean up any existing test log file
    if (fs.existsSync(testLogPath)) {
      fs.unlinkSync(testLogPath);
    }
    
    // Create a fresh logger instance for testing
    testLogger = Logger.getInstance();
  });

  afterEach(() => {
    // Clean up test log file
    if (fs.existsSync(testLogPath)) {
      fs.unlinkSync(testLogPath);
    }
    
    // Close logger to clean up streams
    testLogger.close();
  });

  describe('Singleton Pattern', () => {
    it('should return the same instance', () => {
      const instance1 = Logger.getInstance();
      const instance2 = Logger.getInstance();
      expect(instance1).toBe(instance2);
    });

    it('should use the exported singleton', () => {
      const instance = Logger.getInstance();
      expect(logger).toBe(instance);
    });
  });

  describe('Normal Mode (DBOS Logger)', () => {
    beforeEach(() => {
      testLogger.initialize(false); // Normal mode
    });

    it('should use DBOS logger for info messages', () => {
      const { DBOS } = require('@dbos-inc/dbos-sdk');
      
      testLogger.info('Test info message');
      
      expect(DBOS.logger.info).toHaveBeenCalledWith('Test info message');
    });

    it('should use DBOS logger for warnings', () => {
      const { DBOS } = require('@dbos-inc/dbos-sdk');
      
      testLogger.warn('Test warning');
      
      expect(DBOS.logger.warn).toHaveBeenCalledWith('Test warning');
    });

    it('should use DBOS logger for errors', () => {
      const { DBOS } = require('@dbos-inc/dbos-sdk');
      const error = new Error('Test error');
      
      testLogger.error('Operation failed', error);
      
      expect(DBOS.logger.error).toHaveBeenCalledWith('Operation failed: Test error');
    });

    it('should handle arguments in log messages', () => {
      const { DBOS } = require('@dbos-inc/dbos-sdk');
      
      testLogger.info('User action', { userId: 123, action: 'login' });
      
      expect(DBOS.logger.info).toHaveBeenCalledWith('User action {"userId":123,"action":"login"}');
    });
  });

  describe('Quiet Mode (File Logging)', () => {
    beforeEach(() => {
      testLogger.initialize(true); // Quiet mode
    });

    it('should create log file in quiet mode', (done) => {
      testLogger.info('Test message');
      
      // Give it a moment for file write
      setTimeout(() => {
        expect(fs.existsSync(testLogger.getLogFilePath())).toBe(true);
        done();
      }, 100);
    });

    it('should write formatted log entries to file', (done) => {
      testLogger.info('Test info message');
      testLogger.warn('Test warning');
      testLogger.error('Test error');
      
      setTimeout(() => {
        const logContent = fs.readFileSync(testLogger.getLogFilePath(), 'utf8');
        
        expect(logContent).toContain('[INFO] Test info message');
        expect(logContent).toContain('[WARN] Test warning');
        expect(logContent).toContain('[ERROR] Test error');
        expect(logContent).toMatch(/\[\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z\]/); // ISO timestamp
        
        done();
      }, 100);
    });

    it('should handle error objects with stack traces', (done) => {
      const error = new Error('Test error with stack');
      testLogger.error('Operation failed', error);
      
      setTimeout(() => {
        const logContent = fs.readFileSync(testLogger.getLogFilePath(), 'utf8');
        
        expect(logContent).toContain('[ERROR] Operation failed: Test error with stack');
        expect(logContent).toContain('Stack trace:');
        
        done();
      }, 100);
    });
  });

  describe('Mode Detection', () => {
    it('should report quiet mode correctly', () => {
      testLogger.initialize(true);
      expect(testLogger.isQuietMode()).toBe(true);
      
      testLogger.initialize(false);
      expect(testLogger.isQuietMode()).toBe(false);
    });

    it('should return correct log file path', () => {
      const expectedPath = path.join(process.cwd(), 'server.log');
      expect(testLogger.getLogFilePath()).toBe(expectedPath);
    });
  });

  describe('Cleanup', () => {
    it('should close file streams properly', () => {
      testLogger.initialize(true);
      testLogger.info('Test message');
      
      // Should not throw when closing
      expect(() => testLogger.close()).not.toThrow();
    });
  });
});
