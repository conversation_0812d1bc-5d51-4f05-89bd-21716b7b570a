#!/usr/bin/env ts-node

/**
 * Integration test script for the push-based messaging system
 * Tests real-time message delivery across all client interfaces
 */

import WebSocket from 'ws';
import { EventSource } from 'eventsource';
import { NotificationDispatcher } from '../src/streaming/NotificationDispatcher';
import { EventDrivenMessageProcessor } from '../src/streaming/EventDrivenMessageProcessor';
import { logger } from '../src/utils/Logger';

interface TestResult {
  test: string;
  success: boolean;
  duration: number;
  error?: string;
}

class PushSystemTester {
  private results: TestResult[] = [];
  private serverUrl = 'http://localhost:3000';
  private wsUrl = 'ws://localhost:3000';

  async runAllTests(): Promise<void> {
    console.log('🚀 Starting Push-Based Messaging System Integration Tests\n');

    try {
      // Test 1: WebSocket Push Notifications
      await this.testWebSocketPushNotifications();

      // Test 2: Server-Sent Events
      await this.testServerSentEvents();

      // Test 3: Message Validation with Push Updates
      await this.testMessageValidationPush();

      // Test 4: Multi-Client Notification
      await this.testMultiClientNotification();

      // Test 5: Event-Driven Message Processing
      await this.testEventDrivenProcessing();

      // Test 6: Error Handling and Recovery
      await this.testErrorHandling();

      // Print results
      this.printResults();

    } catch (error) {
      console.error('❌ Test suite failed:', error);
      process.exit(1);
    }
  }

  private async testWebSocketPushNotifications(): Promise<void> {
    const testName = 'WebSocket Push Notifications';
    const startTime = Date.now();

    try {
      console.log('📡 Testing WebSocket push notifications...');

      const ws = new WebSocket(this.wsUrl);
      let messageReceived = false;
      let connectedReceived = false;

      const messagePromise = new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket test timeout'));
        }, 10000);

        ws.on('open', () => {
          console.log('  ✅ WebSocket connected');
        });

        ws.on('message', (data) => {
          try {
            const message = JSON.parse(data.toString());
            console.log('  📨 Received message:', message.type);

            if (message.type === 'connected') {
              connectedReceived = true;
            } else if (message.type === 'message') {
              messageReceived = true;
            }

            if (connectedReceived && messageReceived) {
              clearTimeout(timeout);
              resolve();
            }
          } catch (error) {
            clearTimeout(timeout);
            reject(error);
          }
        });

        ws.on('error', (error) => {
          clearTimeout(timeout);
          reject(error);
        });

        // Send a test message after connection
        ws.on('open', () => {
          setTimeout(() => {
            ws.send(JSON.stringify({
              type: 'chat',
              text: 'Test message for push notifications'
            }));
          }, 1000);
        });
      });

      await messagePromise;
      ws.close();

      this.results.push({
        test: testName,
        success: true,
        duration: Date.now() - startTime
      });

      console.log('  ✅ WebSocket push notifications working correctly\n');

    } catch (error) {
      this.results.push({
        test: testName,
        success: false,
        duration: Date.now() - startTime,
        error: (error as Error).message
      });

      console.log('  ❌ WebSocket push notifications failed:', (error as Error).message, '\n');
    }
  }

  private async testServerSentEvents(): Promise<void> {
    const testName = 'Server-Sent Events';
    const startTime = Date.now();

    try {
      console.log('📡 Testing Server-Sent Events...');

      // First create a session to get session ID
      const sessionResponse = await fetch(`${this.serverUrl}/api/session`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userIdentifier: 'test_sse_user',
          channel: 'test',
          metadata: { test: true }
        })
      });

      const sessionData = await sessionResponse.json();
      const sessionId = sessionData.sessionId || sessionData.id;

      if (!sessionId) {
        throw new Error('Failed to create session for SSE test');
      }

      console.log('  ✅ Created test session:', sessionId);

      const eventSource = new EventSource(`${this.serverUrl}/events/${sessionId}`);
      let connectedReceived = false;

      const ssePromise = new Promise<void>((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('SSE test timeout'));
        }, 10000);

        eventSource.onopen = () => {
          console.log('  ✅ SSE connection established');
        };

        eventSource.onmessage = (event) => {
          try {
            const data = JSON.parse(event.data);
            console.log('  📨 SSE message received:', data);

            if (data.sessionId === sessionId) {
              connectedReceived = true;
              clearTimeout(timeout);
              resolve();
            }
          } catch (error) {
            clearTimeout(timeout);
            reject(error);
          }
        };

        eventSource.onerror = (error) => {
          clearTimeout(timeout);
          reject(new Error('SSE connection error'));
        };
      });

      await ssePromise;
      eventSource.close();

      this.results.push({
        test: testName,
        success: true,
        duration: Date.now() - startTime
      });

      console.log('  ✅ Server-Sent Events working correctly\n');

    } catch (error) {
      this.results.push({
        test: testName,
        success: false,
        duration: Date.now() - startTime,
        error: (error as Error).message
      });

      console.log('  ❌ Server-Sent Events failed:', (error as Error).message, '\n');
    }
  }

  private async testMessageValidationPush(): Promise<void> {
    const testName = 'Message Validation Push Updates';
    const startTime = Date.now();

    try {
      console.log('🔍 Testing message validation with push updates...');

      // This test would require mocking the validation system
      // For now, we'll simulate the validation flow
      const dispatcher = NotificationDispatcher.getInstance();
      
      let validationStartReceived = false;
      let validationCompleteReceived = false;

      // Mock client to receive notifications
      const mockClient = {
        handleNotification: (message: any) => {
          if (message.type === 'validation_request') {
            validationStartReceived = true;
            console.log('  📨 Validation request notification received');
          } else if (message.type === 'validation_result') {
            validationCompleteReceived = true;
            console.log('  📨 Validation result notification received');
          }
        }
      };

      dispatcher.registerClient({
        type: 'repl',
        sessionId: 'validation-test-session',
        conversationId: 123,
        connection: mockClient
      });

      // Simulate validation notifications
      await dispatcher.notifySession('validation-test-session', {
        id: 'val-1',
        type: 'validation_request',
        data: { messageId: 1, character: 'Fora', status: 'validating' },
        timestamp: new Date(),
        priority: 3
      });

      await dispatcher.notifySession('validation-test-session', {
        id: 'val-2',
        type: 'validation_result',
        data: { messageId: 1, character: 'Fora', decision: 'SEND_AS_IS', status: 'completed' },
        timestamp: new Date(),
        priority: 2
      });

      if (validationStartReceived && validationCompleteReceived) {
        this.results.push({
          test: testName,
          success: true,
          duration: Date.now() - startTime
        });
        console.log('  ✅ Message validation push updates working correctly\n');
      } else {
        throw new Error('Not all validation notifications received');
      }

    } catch (error) {
      this.results.push({
        test: testName,
        success: false,
        duration: Date.now() - startTime,
        error: (error as Error).message
      });

      console.log('  ❌ Message validation push updates failed:', (error as Error).message, '\n');
    }
  }

  private async testMultiClientNotification(): Promise<void> {
    const testName = 'Multi-Client Notification';
    const startTime = Date.now();

    try {
      console.log('👥 Testing multi-client notification delivery...');

      const dispatcher = NotificationDispatcher.getInstance();
      const conversationId = 456;
      
      let wsReceived = false;
      let sseReceived = false;
      let replReceived = false;

      // Mock WebSocket client
      const mockWs = {
        readyState: 1,
        send: (data: string) => {
          const message = JSON.parse(data);
          if (message.type === 'message') {
            wsReceived = true;
            console.log('  📨 WebSocket client received message');
          }
        }
      };

      // Mock SSE client
      const mockSSE = {
        write: (data: string) => {
          if (data.includes('data:')) {
            sseReceived = true;
            console.log('  📨 SSE client received message');
          }
        },
        destroyed: false
      };

      // Mock REPL client
      const mockREPL = {
        handleNotification: (message: any) => {
          if (message.type === 'message') {
            replReceived = true;
            console.log('  📨 REPL client received message');
          }
        }
      };

      // Register all clients for the same conversation
      dispatcher.registerClient({
        type: 'websocket',
        sessionId: 'multi-ws-session',
        conversationId,
        connection: mockWs
      });

      dispatcher.registerClient({
        type: 'sse',
        sessionId: 'multi-sse-session',
        conversationId,
        connection: mockSSE
      });

      dispatcher.registerClient({
        type: 'repl',
        sessionId: 'multi-repl-session',
        conversationId,
        connection: mockREPL
      });

      // Send notification to conversation
      await dispatcher.notifyConversation(conversationId, {
        id: 'multi-msg-1',
        type: 'message',
        data: { character: 'Jan', text: 'Multi-client test message' },
        timestamp: new Date(),
        priority: 2,
        conversationId
      });

      if (wsReceived && sseReceived && replReceived) {
        this.results.push({
          test: testName,
          success: true,
          duration: Date.now() - startTime
        });
        console.log('  ✅ Multi-client notification working correctly\n');
      } else {
        throw new Error(`Not all clients received notification: WS=${wsReceived}, SSE=${sseReceived}, REPL=${replReceived}`);
      }

    } catch (error) {
      this.results.push({
        test: testName,
        success: false,
        duration: Date.now() - startTime,
        error: (error as Error).message
      });

      console.log('  ❌ Multi-client notification failed:', (error as Error).message, '\n');
    }
  }

  private async testEventDrivenProcessing(): Promise<void> {
    const testName = 'Event-Driven Message Processing';
    const startTime = Date.now();

    try {
      console.log('⚡ Testing event-driven message processing...');

      const processor = EventDrivenMessageProcessor.getInstance();
      const conversationId = 789;

      // Start processing
      processor.startConversationProcessing(conversationId);

      let eventReceived = false;
      processor.on('message_queued', () => {
        eventReceived = true;
        console.log('  📨 Message queued event received');
      });

      // Emit test event
      processor.emitMessageEvent({
        type: 'queued',
        messageId: 1,
        conversationId,
        character: 'Lou',
        timestamp: new Date()
      });

      // Give it a moment to process
      await new Promise(resolve => setTimeout(resolve, 100));

      const stats = processor.getProcessingStats();
      
      if (eventReceived && stats.activeConversations >= 1) {
        this.results.push({
          test: testName,
          success: true,
          duration: Date.now() - startTime
        });
        console.log('  ✅ Event-driven processing working correctly\n');
      } else {
        throw new Error('Event-driven processing not working as expected');
      }

      // Clean up
      processor.stopConversationProcessing(conversationId);

    } catch (error) {
      this.results.push({
        test: testName,
        success: false,
        duration: Date.now() - startTime,
        error: (error as Error).message
      });

      console.log('  ❌ Event-driven processing failed:', (error as Error).message, '\n');
    }
  }

  private async testErrorHandling(): Promise<void> {
    const testName = 'Error Handling and Recovery';
    const startTime = Date.now();

    try {
      console.log('🛡️ Testing error handling and recovery...');

      const dispatcher = NotificationDispatcher.getInstance();
      
      // Test with broken WebSocket
      const brokenWs = {
        readyState: 1,
        send: () => {
          throw new Error('Simulated WebSocket error');
        }
      };

      dispatcher.registerClient({
        type: 'websocket',
        sessionId: 'error-test-session',
        connection: brokenWs
      });

      const results = await dispatcher.notifySession('error-test-session', {
        id: 'error-msg-1',
        type: 'message',
        data: { character: 'Fora', text: 'Error test message' },
        timestamp: new Date(),
        priority: 1
      });

      if (results.length === 1 && !results[0].success && results[0].error) {
        this.results.push({
          test: testName,
          success: true,
          duration: Date.now() - startTime
        });
        console.log('  ✅ Error handling working correctly\n');
      } else {
        throw new Error('Error handling not working as expected');
      }

    } catch (error) {
      this.results.push({
        test: testName,
        success: false,
        duration: Date.now() - startTime,
        error: (error as Error).message
      });

      console.log('  ❌ Error handling test failed:', (error as Error).message, '\n');
    }
  }

  private printResults(): void {
    console.log('📊 Test Results Summary');
    console.log('========================\n');

    const passed = this.results.filter(r => r.success).length;
    const failed = this.results.filter(r => !r.success).length;
    const total = this.results.length;

    this.results.forEach(result => {
      const status = result.success ? '✅ PASS' : '❌ FAIL';
      const duration = `${result.duration}ms`;
      console.log(`${status} ${result.test} (${duration})`);
      
      if (!result.success && result.error) {
        console.log(`    Error: ${result.error}`);
      }
    });

    console.log(`\n📈 Summary: ${passed}/${total} tests passed (${failed} failed)`);
    
    if (failed === 0) {
      console.log('🎉 All tests passed! Push-based messaging system is working correctly.');
    } else {
      console.log('⚠️ Some tests failed. Please review the errors above.');
      process.exit(1);
    }
  }
}

// Run tests if this script is executed directly
if (require.main === module) {
  const tester = new PushSystemTester();
  tester.runAllTests().catch(error => {
    console.error('Test runner failed:', error);
    process.exit(1);
  });
}
