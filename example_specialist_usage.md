# Specialist Injection Feature

This document demonstrates how the specialist injection feature works in the ForaChat system.

## Overview

The theme analysis system now identifies potential specialists who can help with specific workplace issues. When specialists are identified, they are injected into <PERSON><PERSON>'s system prompt so she can refer to them as her friends who can help.

## How It Works

1. **Theme Analysis**: The `theme_analysis_system.md` prompt analyzes user input and identifies relevant specialists
2. **Specialist Identification**: Specialists are identified based on the user's request and stored in the `specialists` field
3. **Context Injection**: For <PERSON><PERSON> specifically, specialist context is injected into her system prompt
4. **Friend References**: <PERSON><PERSON> is instructed to refer to specialists as her friends who can help

## Example Flow

### User Input
```
"My coworker keeps interrupting me in meetings"
```

### Theme Analysis Response
```json
{
  "theme": "conflict resolution",
  "skills": ["Assertiveness", "Conflict Resolution", "Professional Boundaries"],
  "reasoning": "Workplace interpersonal conflict requiring boundary setting and conflict management",
  "specialists": ["des"]
}
```

### <PERSON><PERSON>'s Enhanced System Prompt
When <PERSON><PERSON> decides whether to respond, her system prompt is enhanced with:

```
**CURRENT CONVERSATION SPECIALISTS:**
The following specialists have been identified as relevant to this conversation: des
When appropriate, refer to these specialists as your friends who can help with their specific expertise.
```

### Expected Fora Response
```
"Ugh, that's so frustrating! My friend Des is incredible at de-escalating conflicts - they could really help you navigate this situation with your coworker."
```

## Available Specialists

- **Nat**: Expert negotiator (salary negotiation, promotions, advocating for resources)
- **Irv**: Interview prep specialist (behavioral questions, career narratives)
- **Ren**: Resume perfectionist (resume writing, LinkedIn optimization, personal branding)
- **Des**: Conflict de-escalation expert (difficult conversations, managing difficult colleagues)
- **Bon**: Boundary setting specialist (saying no, managing workload, challenging coworkers)
- **Pri**: Presentation wizard (presentation skills, storytelling, public speaking)

## Technical Implementation

### Key Changes Made

1. **ThemeAnalysisResult Interface**: Added `specialists: string[]` field
2. **Theme Analysis System**: Updated to identify and return specialists
3. **Character Decision Service**: Enhanced to inject specialist context for Fora
4. **Fora System Prompt**: Updated with specialist referral instructions

### Code Locations

- `src/core/ThemeAnalysisService.ts`: Updated interface and result parsing
- `src/core/CharacterDecisionService.ts`: Added specialist context injection
- `prompts/fora_system.md`: Added specialist referral instructions
- `prompts/theme_analysis_system.md`: Already had specialist identification

### Testing

The feature is tested in `tests/core/SpecialistInjection.test.ts` which verifies:
- Specialist context formatting
- Injection into Fora's system prompt only
- Inclusion in decision prompts for all characters
- Proper handling when no specialists are identified

## Usage Examples

### Salary Negotiation
- **Input**: "How do I ask my boss for a raise?"
- **Specialist**: `nat`
- **Fora Response**: "My friend Nat is amazing at negotiation - they could really help you with this"

### Interview Preparation
- **Input**: "I have a job interview next week and I'm nervous"
- **Specialist**: `irv`
- **Fora Response**: "Irv is a friend of mine who's fantastic at interview prep - you should definitely connect with them"

### Conflict Resolution
- **Input**: "My manager is being really difficult"
- **Specialist**: `des`
- **Fora Response**: "I know Des personally and they're incredible at de-escalating conflicts"

This feature makes Fora's responses more helpful by connecting users with the right specialists for their specific needs.
