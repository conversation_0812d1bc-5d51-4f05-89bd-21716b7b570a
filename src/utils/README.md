# Central Logger Utility

The `Logger` utility provides a centralized logging solution that automatically switches between DBOS.logger and file logging based on the application's quiet mode setting.

## Features

- **Automatic Mode Switching**: Uses DBOS.logger in normal mode, writes to `server.log` in quiet mode
- **Singleton Pattern**: Single instance across the entire application
- **Multiple Log Levels**: info, warn, error, debug
- **Graceful Fallbacks**: Falls back to console logging if DBOS.logger is unavailable
- **Structured Logging**: Consistent timestamp and level formatting in file mode
- **Error Handling**: Proper error logging with stack traces

## Usage

### Basic Import and Usage

```typescript
import { logger } from '../utils/Logger';

// Log different levels
logger.info('Application started successfully');
logger.warn('Configuration value missing, using default');
logger.error('Database connection failed', error);
logger.debug('Processing user request', { userId: 123, action: 'login' });
```

### Initialization

The logger is automatically initialized by `ForaChatApp` based on the quiet mode setting:

```typescript
// In ForaChatApp constructor
logger.initialize(this.quietMode);
```

### In DBOS Workflows and Steps

Replace existing `DBOS.logger` calls with the central logger:

```typescript
// Before
DBOS.logger.info('Processing workflow step');
DBOS.logger.error(`Error: ${error.message}`);

// After
import { logger } from '../utils/Logger';

logger.info('Processing workflow step');
logger.error('Error occurred', error);
```

### Error Logging

The logger provides enhanced error logging:

```typescript
try {
  // Some operation
} catch (error) {
  // Logs both message and error details, including stack trace
  logger.error('Failed to process user request', error);
}
```

### Checking Logger Mode

```typescript
if (logger.isQuietMode()) {
  // Logger is writing to file
  console.log(`Logs are being written to: ${logger.getLogFilePath()}`);
} else {
  // Logger is using DBOS.logger
}
```

## Log Levels

### info()
- General application information
- Successful operations
- Status updates

### warn()
- Non-critical issues
- Deprecated usage
- Configuration warnings

### error()
- Critical errors
- Exception handling
- Failed operations
- Automatically includes stack traces for Error objects

### debug()
- Detailed debugging information
- Development-time logging
- Verbose operation details

## File Logging Format

When in quiet mode, logs are written to `server.log` with the following format:

```
[2024-07-06T10:30:45.123Z] [INFO] Application started successfully
[2024-07-06T10:30:46.456Z] [WARN] Configuration value missing, using default
[2024-07-06T10:30:47.789Z] [ERROR] Database connection failed: Connection timeout
[2024-07-06T10:30:47.790Z] [ERROR] Stack trace: Error: Connection timeout
    at Database.connect (/path/to/file.js:123:45)
    ...
```

## Migration Guide

To migrate existing code from `DBOS.logger` to the central logger:

1. **Add import**:
   ```typescript
   import { logger } from '../utils/Logger';
   ```

2. **Replace calls**:
   ```typescript
   // Replace
   DBOS.logger.info('message');
   DBOS.logger.error(`Error: ${error.message}`);
   
   // With
   logger.info('message');
   logger.error('Error occurred', error);
   ```

3. **Update error handling**:
   ```typescript
   // Before
   DBOS.logger.error(`Error: ${(error as Error).message}`);
   
   // After (logger handles Error objects automatically)
   logger.error('Operation failed', error);
   ```

## Benefits

1. **Consistent Logging**: All application logs use the same format and routing
2. **Environment Awareness**: Automatically adapts to quiet mode without code changes
3. **Better Error Handling**: Enhanced error logging with stack traces
4. **Centralized Configuration**: Single point to control logging behavior
5. **Graceful Degradation**: Falls back to console if DBOS.logger is unavailable
6. **Easy Testing**: Can be easily mocked or configured for test environments

## Cleanup

The logger automatically handles cleanup when the application shuts down. The `ForaChatApp.shutdown()` method calls `logger.close()` to properly close file streams.
