import { DBOS } from '@dbos-inc/dbos-sdk';
import { QueuedMessage } from '../models/types';
import { ChatService } from '../core/ChatService';
import { ConversationService } from '../core/ConversationService';
import { MessageQueueService } from '../core/MessageQueueService';
import { MessageValidationService } from '../core/MessageValidationService';
import { PromptService } from '../core/PromptService';
import { SessionManager } from './SessionManager';
import { MessageStreamer } from './MessageStreamer';
import { EventDrivenMessageProcessor } from './EventDrivenMessageProcessor';
import { logger } from '../utils/Logger';
import WebSocket from 'ws';

// ===== MESSAGE QUEUE PROCESSOR COMPONENT =====

export class MessageQueueProcessor {
  private eventProcessor: EventDrivenMessageProcessor;

  constructor(
    private sessionManager: SessionManager,
    private messageStreamer: MessageStreamer,
    private chatService: ChatService
  ) {
    this.eventProcessor = EventDrivenMessageProcessor.getInstance();
  }

  // Start event-driven processing for delayed character thoughts
  async startDelayedThoughtsProcessing(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    // Start event-driven processing for this conversation
    this.eventProcessor.startConversationProcessing(session.conversationId);

    logger.info(`🚀 Started event-driven message processing for session ${sessionId} (conversation ${session.conversationId})`);
  }

  // Stop event-driven processing for a session
  async stopDelayedThoughtsProcessing(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    // Stop event-driven processing for this conversation
    this.eventProcessor.stopConversationProcessing(session.conversationId);

    logger.info(`🛑 Stopped event-driven message processing for session ${sessionId} (conversation ${session.conversationId})`);
  }

  // Legacy method for backward compatibility - now uses event-driven approach
  async pollForDelayedThoughts(sessionId: string): Promise<void> {
    logger.info(`🔄 Converting polling request to event-driven processing for session ${sessionId}`);
    await this.startDelayedThoughtsProcessing(sessionId);
  }

  // Reframe queued messages so later ones acknowledge earlier ones
  private async reframeQueuedMessages(sessionId: string, messages: QueuedMessage[]): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    // Sort messages by priority and scheduled time (earliest first)
    const sortedMessages = [...messages].sort((a, b) => {
      if (a.priority !== b.priority) return a.priority - b.priority;
      return new Date(a.scheduled_at!).getTime() - new Date(b.scheduled_at!).getTime();
    });

    // For messages after the first one, reframe them to acknowledge previous messages
    for (let i = 1; i < sortedMessages.length; i++) {
      const currentMessage = sortedMessages[i];
      const previousMessages = sortedMessages.slice(0, i);

      try {
        // Build context with previous messages that will appear before this one
        const precedingContext = previousMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n');

        // Get recent conversation context
        const recentMessages = await ConversationService.getDelayedThoughts(session.conversationId);
        const conversationContext = recentMessages.slice(-3).map(msg => `${msg.character}: ${msg.text}`).join('\n');

        const reframingPrompt = `Based on this conversation and the messages that will appear before yours, reframe your response to acknowledge what others have said:

Conversation context:
${conversationContext}

Messages that will appear before yours:
${precedingContext}

Your original message: "${currentMessage.text}"

Reframe your message to acknowledge the preceding messages while maintaining your character's voice. Respond with JSON in this format:
{
  "reply": [{"character": "${currentMessage.character}", "text": "your reframed message", "delay": ${currentMessage.delay_ms}}],
  "skills": [],
  "theme": "reframed response"
}`;

        const systemPrompt = await PromptService.getSystemPrompt(`${currentMessage.character.toLowerCase()}_system`);
        const llmResponse = await this.chatService.getLLMService().generate(systemPrompt, reframingPrompt);

        if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
          // Update the message text in the queue
          await MessageQueueService.updateMessageText(currentMessage.id, llmResponse.reply[0].text);
          logger.info(`Reframed queued message for ${currentMessage.character}: ${llmResponse.reply[0].text.substring(0, 50)}...`);
        }
      } catch (error) {
        logger.error(`Error reframing queued message for ${currentMessage.character}`, error);
        // Keep original message if reframing fails
      }
    }
  }
}
