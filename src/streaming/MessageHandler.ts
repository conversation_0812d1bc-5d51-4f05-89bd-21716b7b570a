import { DBOS } from '@dbos-inc/dbos-sdk';
import { ForaChat } from '../operations';
import { DelayedMessage } from '../models/types';
import { ChatService } from '../core/ChatService';
import { ConversationService } from '../core/ConversationService';
import { MessageQueueService } from '../core/MessageQueueService';
import { PromptService } from '../core/PromptService';
import { SessionManager } from './SessionManager';
import { MessageStreamer } from './MessageStreamer';
import { EngagementMonitor } from './EngagementMonitor';
import { ChatResponse } from './types';
import { logger } from '../utils/Logger';

// ===== MESSAGE HANDLER COMPONENT =====

export class MessageHandler {
  constructor(
    private sessionManager: SessionManager,
    private messageStreamer: MessageStreamer,
    private engagementMonitor: EngagementMonitor,
    private chatService: ChatService,
    private messageQueueProcessor?: any // Add optional MessageQueueProcessor
  ) {}

  async handleMessage(sessionId: string, message: any): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    switch (message.type) {
      case 'chat':
        await this.handleChatMessage(sessionId, message.text);
        break;
      case 'interrupt':
        await this.handleInterrupt(sessionId, message.text);
        break;
      case 'start_extended_workflow':
        // This will be handled by WorkflowManager
        break;
      case 'get_extended_workflow_status':
        // This will be handled by WorkflowManager
        break;
      default:
        this.messageStreamer.sendError(sessionId, 'Unknown message type');
    }
  }

  async handleChatMessage(sessionId: string, text: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    // Update user activity tracking
    this.engagementMonitor.updateUserActivity(sessionId);
    await this.engagementMonitor.updateSessionActivity(sessionId);

    if (session.isStreaming) {
      // User interrupted while messages are being streamed
      await this.handleInterrupt(sessionId, text);
      return;
    }

    try {
      logger.info(`=== STREAMING CHAT MESSAGE PROCESSING ===`);
      logger.info(`Session ID: ${sessionId}`);
      logger.info(`User Message: "${text}"`);

      let result;

      // If we have a conversation ID, continue the conversation, otherwise start new
      if (session.conversationId) {
        logger.info(`Existing Conversation ID: ${session.conversationId}`);
        logger.info(`Continuing existing conversation`);
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text, session.conversationId);
        result = await handle.getResult();
      } else {
        logger.info(`No existing conversation - starting new conversation`);
        const handle = await DBOS.startWorkflow(ForaChat).chatWorkflow(text);
        result = await handle.getResult();
        session.conversationId = result.conversationId;

        // Update session with new conversation ID
        if (result.conversationId) {
          try {
            await ForaChat.updateSessionConversation(session.sessionId, result.conversationId);
          } catch (error) {
            logger.error(`Failed to update session conversation`, error);
          }
        }
      }

      const response = result;

      if (!response) {
        this.messageStreamer.sendError(sessionId, 'No response from chat service');
        return;
      }

      if (response.reply && Array.isArray(response.reply)) {
        logger.info(`=== CHAT WORKFLOW COMPLETED ===`);
        logger.info(`Final Conversation ID: ${response.conversationId}`);
        logger.info(`Response Messages: ${response.reply.length}`);
        logger.info(`Theme: ${response.theme || 'Not set'}`);
        logger.info(`Skills: ${response.skills ? JSON.stringify(response.skills) : 'Not set'}`);
        logger.info(`Session will continue with conversation ID: ${session.conversationId}`);
        logger.info(`=======================================`);

        this.messageStreamer.sendMessage(sessionId, {
          type: 'chat_start',
          theme: response.theme || 'Chat Response'
        });

        if (response.reply && response.reply.length > 0) {
          // Stream the immediate messages and start polling
          await this.messageStreamer.streamDelayedMessages(sessionId, response.reply, response.skills);
        } else {
          // No immediate messages, but we still need to start polling for queued character thoughts
          logger.info(`No immediate messages to stream, but starting polling for queued character thoughts`);

          // Send chat_complete immediately since there are no messages to stream
          this.messageStreamer.sendMessage(sessionId, {
            type: 'chat_complete',
            skills: response.skills || []
          });

          // Start polling for delayed character thoughts directly
          if (this.messageQueueProcessor) {
            await this.messageQueueProcessor.pollForDelayedThoughts(sessionId);
          }
        }

        // Get the current highest message ID to avoid re-polling initial messages
        const allMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (allMessages.length > 0) {
          session.lastMessageId = Math.max(...allMessages.map(m => m.id || 0));
          logger.info(`Set lastMessageId to ${session.lastMessageId} to avoid re-polling initial messages`);
        }
      } else {
        this.messageStreamer.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.messageStreamer.sendError(sessionId, 'Failed to process chat message', (error as Error).message);
    }
  }

  async handleInterrupt(sessionId: string, newText: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session) return;

    const wasExtendedWorkflowActive = session.extendedWorkflowActive;

    session.interrupted = true;
    session.lastUserMessage = newText; // Track the user's latest message
    this.sessionManager.clearTimeouts(sessionId);

    // Cancel any pending character thoughts that haven't been processed yet
    await this.cancelPendingCharacterThoughts(sessionId);

    // Send appropriate interruption message
    const interruptMessage = wasExtendedWorkflowActive
      ? 'Extended conversation interrupted. Processing your new message...'
      : 'Processing your new message...';

    this.messageStreamer.sendMessage(sessionId, {
      type: 'interrupted',
      message: interruptMessage,
      extendedWorkflowInterrupted: wasExtendedWorkflowActive
    });

    // Get the messages that were already sent
    const sentMessages: DelayedMessage[] = [];

    // Calculate which messages were already sent based on timing
    let cumulativeDelay = 0;

    for (const message of session.pendingMessages) {
      cumulativeDelay += (message.delay || 0);
      // If enough time has passed for this message to be sent, include it
      if (cumulativeDelay <= 1000) { // Rough estimate - in real implementation would track start time
        sentMessages.push(message);
      } else {
        break;
      }
    }

    try {
      // Use the interrupted chat workflow with context
      const handle = await DBOS.startWorkflow(ForaChat).interruptedChatWorkflow(
        newText,
        sentMessages.map(msg => ({ character: msg.character, text: msg.text })),
        session.conversationId
      );
      const result: ChatResponse = await handle.getResult();

      if (result.error) {
        this.messageStreamer.sendError(sessionId, result.error, result.details);
        return;
      }

      if (result.reply && Array.isArray(result.reply)) {
        this.messageStreamer.sendMessage(sessionId, {
          type: 'chat_start',
          theme: result.theme || 'Chat Response (Interrupted)'
        });

        await this.messageStreamer.streamDelayedMessages(sessionId, result.reply, result.skills);

        // Get the current highest message ID to avoid re-polling initial messages
        const allMessages = await ConversationService.getDelayedThoughts(session.conversationId!);
        if (allMessages.length > 0) {
          session.lastMessageId = Math.max(...allMessages.map(m => m.id || 0));
          logger.info(`Set lastMessageId to ${session.lastMessageId} after interrupt to avoid re-polling initial messages`);
        }
      } else {
        this.messageStreamer.sendError(sessionId, 'Invalid response format from LLM');
      }
    } catch (error) {
      this.messageStreamer.sendError(sessionId, 'Failed to process interrupted message', (error as Error).message);
    }
  }

  // Cancel pending character thoughts when user interrupts
  private async cancelPendingCharacterThoughts(sessionId: string): Promise<void> {
    const session = this.sessionManager.getSession(sessionId);
    if (!session || !session.conversationId) return;

    try {
      // Get any pending messages from the queue
      const pendingMessages = await MessageQueueService.getPendingMessages(session.conversationId);

      if (pendingMessages.length > 0) {
        logger.info(`Handling ${pendingMessages.length} pending messages due to user interruption`);

        // For each pending message, decide whether to cancel or reframe
        for (const message of pendingMessages) {
          try {
            // If the message was scheduled very soon, reframe it to acknowledge the interruption
            const timeUntilScheduled = new Date(message.scheduled_at!).getTime() - Date.now();

            if (timeUntilScheduled < 10000) { // Less than 10 seconds away
              const reframingPrompt = `The user just sent a new message, interrupting the conversation.

Your original planned response was: "${message.text}"
User's new message: "${session.lastUserMessage || 'new input'}"

Generate a brief response that acknowledges the user's new input instead. Keep your character voice but pivot to address what they just said. Respond with JSON in this format:
{
  "reply": [{"character": "${message.character}", "text": "your new response", "delay": ${message.delay_ms}}],
  "skills": [],
  "theme": "interruption response"
}`;

              const systemPrompt = await PromptService.getSystemPrompt(`${message.character.toLowerCase()}_system`);
              const llmResponse = await this.chatService.getLLMService().generate(systemPrompt, reframingPrompt);

              if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
                // Update the message with the new response
                await MessageQueueService.updateMessageText(message.id, llmResponse.reply[0].text);
                logger.info(`Reframed ${message.character}'s queued message due to interruption`);
              }
            } else {
              // Cancel messages that were scheduled further in the future
              await MessageQueueService.updateMessageStatus(message.id, 'CANCELLED');
              logger.info(`Cancelled ${message.character}'s queued message due to interruption`);
            }
          } catch (error) {
            logger.error(`Error handling ${message.character}'s queued message`, error);
          }
        }
      }
    } catch (error) {
      logger.error(`Error handling pending messages during interruption`, error);
    }
  }
}
