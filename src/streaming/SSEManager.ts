import { Request, Response } from 'express';
import { NotificationDispatcher, NotificationTarget } from './NotificationDispatcher';
import { logger } from '../utils/Logger';

// ===== SERVER-SENT EVENTS MANAGER =====

export interface SSEConnection {
  sessionId: string;
  conversationId?: number;
  response: Response;
  lastEventId?: string;
  connected: boolean;
  heartbeatInterval?: NodeJS.Timeout;
}

export class SSEManager {
  private static instance: SSEManager;
  private connections: Map<string, SSEConnection> = new Map();
  private notificationDispatcher: NotificationDispatcher;
  
  // Heartbeat interval (30 seconds)
  private readonly HEARTBEAT_INTERVAL = 30000;
  
  // Connection timeout (5 minutes of inactivity)
  private readonly CONNECTION_TIMEOUT = 5 * 60 * 1000;

  private constructor() {
    this.notificationDispatcher = NotificationDispatcher.getInstance();
    this.setupCleanupInterval();
  }

  static getInstance(): SSEManager {
    if (!SSEManager.instance) {
      SSEManager.instance = new SSEManager();
    }
    return SSEManager.instance;
  }

  // ===== CONNECTION MANAGEMENT =====

  createConnection(req: Request, res: Response, sessionId: string, conversationId?: number): SSEConnection {
    // Set SSE headers
    res.writeHead(200, {
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers': 'Cache-Control',
      'Access-Control-Allow-Credentials': 'true'
    });

    // Handle client disconnect
    req.on('close', () => {
      this.closeConnection(sessionId);
    });

    req.on('aborted', () => {
      this.closeConnection(sessionId);
    });

    // Create connection object
    const connection: SSEConnection = {
      sessionId,
      conversationId,
      response: res,
      lastEventId: req.headers['last-event-id'] as string,
      connected: true
    };

    // Store connection
    this.connections.set(sessionId, connection);

    // Register with notification dispatcher
    const target: NotificationTarget = {
      type: 'sse',
      sessionId,
      conversationId,
      connection: res,
      metadata: {
        userAgent: req.headers['user-agent'],
        ip: req.ip
      }
    };

    this.notificationDispatcher.registerClient(target);

    // Start heartbeat
    this.startHeartbeat(connection);

    // Send initial connection event
    this.sendEvent(connection, {
      id: this.generateEventId(),
      event: 'connected',
      data: JSON.stringify({
        sessionId,
        conversationId,
        timestamp: new Date().toISOString()
      })
    });

    logger.info(`📡 SSE connection established for session ${sessionId.substring(0, 8)}... ${conversationId ? `(conversation ${conversationId})` : ''}`);

    return connection;
  }

  closeConnection(sessionId: string): void {
    const connection = this.connections.get(sessionId);
    if (!connection) return;

    // Stop heartbeat
    if (connection.heartbeatInterval) {
      clearInterval(connection.heartbeatInterval);
    }

    // Mark as disconnected
    connection.connected = false;

    // Unregister from notification dispatcher
    this.notificationDispatcher.unregisterClient(sessionId, 'sse');

    // Remove from connections
    this.connections.delete(sessionId);

    // Close response if still open
    if (!connection.response.destroyed) {
      try {
        connection.response.end();
      } catch (error) {
        // Response may already be closed
      }
    }

    logger.info(`📡 SSE connection closed for session ${sessionId.substring(0, 8)}...`);
  }

  // ===== EVENT SENDING =====

  private sendEvent(connection: SSEConnection, event: {
    id?: string;
    event?: string;
    data: string;
    retry?: number;
  }): boolean {
    if (!connection.connected || connection.response.destroyed) {
      return false;
    }

    try {
      let eventString = '';
      
      if (event.id) {
        eventString += `id: ${event.id}\n`;
      }
      
      if (event.event) {
        eventString += `event: ${event.event}\n`;
      }
      
      if (event.retry) {
        eventString += `retry: ${event.retry}\n`;
      }
      
      eventString += `data: ${event.data}\n\n`;
      
      connection.response.write(eventString);
      return true;
    } catch (error) {
      logger.error(`Error sending SSE event to session ${connection.sessionId}`, error);
      this.closeConnection(connection.sessionId);
      return false;
    }
  }

  // ===== HEARTBEAT =====

  private startHeartbeat(connection: SSEConnection): void {
    connection.heartbeatInterval = setInterval(() => {
      if (!connection.connected) {
        return;
      }

      const success = this.sendEvent(connection, {
        event: 'heartbeat',
        data: JSON.stringify({
          timestamp: new Date().toISOString()
        })
      });

      if (!success) {
        this.closeConnection(connection.sessionId);
      }
    }, this.HEARTBEAT_INTERVAL);
  }

  // ===== UTILITY METHODS =====

  private generateEventId(): string {
    return `sse_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
  }

  private setupCleanupInterval(): void {
    // Clean up stale connections every minute
    setInterval(() => {
      const now = Date.now();
      const staleConnections: string[] = [];

      for (const [sessionId, connection] of this.connections.entries()) {
        if (connection.response.destroyed || !connection.connected) {
          staleConnections.push(sessionId);
        }
      }

      for (const sessionId of staleConnections) {
        this.closeConnection(sessionId);
      }

      if (staleConnections.length > 0) {
        logger.info(`🧹 Cleaned up ${staleConnections.length} stale SSE connections`);
      }
    }, 60000); // Every minute
  }

  // ===== PUBLIC API =====

  getConnection(sessionId: string): SSEConnection | undefined {
    return this.connections.get(sessionId);
  }

  getActiveConnections(): SSEConnection[] {
    return Array.from(this.connections.values()).filter(conn => conn.connected);
  }

  getConnectionCount(): number {
    return this.connections.size;
  }

  // Send a message to a specific SSE connection
  sendMessage(sessionId: string, event: string, data: any): boolean {
    const connection = this.connections.get(sessionId);
    if (!connection) {
      return false;
    }

    return this.sendEvent(connection, {
      id: this.generateEventId(),
      event,
      data: JSON.stringify(data)
    });
  }

  // Broadcast to all SSE connections
  broadcast(event: string, data: any): number {
    let successCount = 0;
    
    for (const connection of this.connections.values()) {
      if (this.sendEvent(connection, {
        id: this.generateEventId(),
        event,
        data: JSON.stringify(data)
      })) {
        successCount++;
      }
    }

    return successCount;
  }

  // Send to all connections in a conversation
  sendToConversation(conversationId: number, event: string, data: any, excludeSession?: string): number {
    let successCount = 0;
    
    for (const connection of this.connections.values()) {
      if (connection.conversationId === conversationId && 
          connection.sessionId !== excludeSession) {
        if (this.sendEvent(connection, {
          id: this.generateEventId(),
          event,
          data: JSON.stringify(data)
        })) {
          successCount++;
        }
      }
    }

    return successCount;
  }
}
