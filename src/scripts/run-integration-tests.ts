#!/usr/bin/env node

import { IntegrationTestRunner } from './integration-test-runner';
import * as path from 'path';
import * as fs from 'fs';

interface CLIArgs {
  promptsFile?: string;
  outputFile?: string;
  serverUrl?: string;
  serverPort?: number;
  maxDelayedWaitTime?: number;
  logLevel?: 'verbose' | 'normal' | 'minimal';
  skipDelayWait?: boolean;
  help?: boolean;
}

function parseArgs(): CLIArgs {
  const args: CLIArgs = {};
  const argv = process.argv.slice(2);

  for (let i = 0; i < argv.length; i++) {
    const arg = argv[i];
    const nextArg = argv[i + 1];

    switch (arg) {
      case '--prompts-file':
      case '-f':
        args.promptsFile = nextArg;
        i++;
        break;
      case '--output-file':
      case '-o':
        args.outputFile = nextArg;
        i++;
        break;
      case '--server-url':
        args.serverUrl = nextArg;
        i++;
        break;
      case '--server-port':
        args.serverPort = parseInt(nextArg);
        i++;
        break;
      case '--max-delayed-wait':
        args.maxDelayedWaitTime = parseInt(nextArg) * 1000; // Convert seconds to milliseconds
        i++;
        break;
      case '--log-level':
        if (['verbose', 'normal', 'minimal'].includes(nextArg)) {
          args.logLevel = nextArg as 'verbose' | 'normal' | 'minimal';
        }
        i++;
        break;
      case '--skip-delay-wait':
        args.skipDelayWait = true;
        break;
      case '--help':
      case '-h':
        args.help = true;
        break;
    }
  }

  return args;
}

function showHelp(): void {
  console.log(`
ForaChat Integration Test Runner

Usage: npm run test:integration -- [options]

Options:
  --prompts-file, -f <file>     Path to file containing test prompts (required)
  --output-file, -o <file>      Path to save test results (optional)
  --server-url <url>            Server URL (default: localhost)
  --server-port <port>          Server port (default: 3000)
  --max-delayed-wait <seconds>  Max time to wait for delayed messages (default: 30)
  --log-level <level>           Log level: verbose, normal, minimal (default: normal)
  --skip-delay-wait             Skip waiting for delays, just capture queue info
  --help, -h                    Show this help message

Prompts File Format:
  Text file with one prompt per line, or JSON file with structured prompts.
  
  Text file example:
    How do I handle difficult conversations?
    What's the best way to give feedback?
    # This is a comment and will be ignored
    
  JSON file example:
    {
      "prompts": [
        {
          "id": "test_1",
          "text": "How do I handle difficult conversations?",
          "expectedTheme": "Difficult Conversations",
          "expectedSkills": ["communication", "conflict resolution"],
          "timeout": 60000
        }
      ]
    }

Examples:
  npm run test:integration -- --prompts-file test-prompts.txt
  npm run test:integration -- -f prompts.json -o results.json --log-level verbose
  npm run test:integration -- -f prompts.txt --max-delayed-wait 45
  npm run test:integration -- -f prompts.txt --skip-delay-wait
`);
}

async function main(): Promise<void> {
  const args = parseArgs();

  if (args.help) {
    showHelp();
    process.exit(0);
  }

  if (!args.promptsFile) {
    console.error('❌ Error: --prompts-file is required');
    console.error('Use --help for usage information');
    process.exit(1);
  }

  const promptsPath = path.resolve(args.promptsFile);
  if (!fs.existsSync(promptsPath)) {
    console.error(`❌ Error: Prompts file not found: ${promptsPath}`);
    process.exit(1);
  }

  console.log('🚀 ForaChat Integration Test Runner');
  console.log('=====================================');

  const runner = new IntegrationTestRunner({
    serverUrl: args.serverUrl || 'localhost',
    serverPort: args.serverPort || 3000,
    maxDelayedWaitTime: args.maxDelayedWaitTime || 30000,
    logLevel: args.logLevel || 'normal',
    outputFile: args.outputFile,
    skipDelayWait: args.skipDelayWait || false
  });

  let prompts;
  let results;

  try {
    // Load prompts
    console.log(`📂 Loading prompts from: ${promptsPath}`);
    
    if (promptsPath.endsWith('.json')) {
      prompts = runner.loadPromptsFromJSON(promptsPath);
    } else {
      prompts = runner.loadPromptsFromFile(promptsPath);
    }
    
    console.log(`✅ Loaded ${prompts.length} test prompts`);

    // Start server
    await runner.startServer();

    // Run tests
    results = await runner.runTests(prompts);

    // Generate and save results
    await runner.saveResults(results, args.outputFile);

    // Print summary
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const successRate = ((successful / results.length) * 100).toFixed(1);

    console.log('\n📊 Test Summary');
    console.log('================');
    console.log(`Total Tests: ${results.length}`);
    console.log(`Successful: ${successful} ✅`);
    console.log(`Failed: ${failed} ❌`);
    console.log(`Success Rate: ${successRate}%`);

    if (failed > 0) {
      console.log('\n❌ Failed Tests:');
      results.filter(r => !r.success).forEach(result => {
        console.log(`  - ${result.id}: ${result.error}`);
      });
    }

    // Exit with appropriate code
    process.exit(failed > 0 ? 1 : 0);

  } catch (error) {
    console.error(`\n❌ Test run failed: ${(error as Error).message}`);
    console.error((error as Error).stack);
    process.exit(1);
  } finally {
    // Cleanup
    try {
      await runner.cleanup();
    } catch (error) {
      console.error(`⚠️ Cleanup error: ${(error as Error).message}`);
    }
  }
}

// Handle process termination gracefully
process.on('SIGINT', () => {
  console.log('\n🛑 Test run interrupted by user');
  process.exit(130);
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Test run terminated');
  process.exit(143);
});

// Run the main function
main().catch((error) => {
  console.error('💥 Unexpected error:', error);
  process.exit(1);
});
