#!/usr/bin/env node

import { ForaChatApp } from '../ForaChatApp';
import { logger } from '../utils/Logger';
import * as fs from 'fs';
import * as path from 'path';
import * as http from 'http';
import { v4 as uuidv4 } from 'uuid';

interface TestPrompt {
  id: string;
  prompt: string;
  expectedTheme?: string;
  expectedSkills?: string[];
  timeout?: number; // in milliseconds
}

interface CharacterReply {
  character: string;
  text: string;
  delay: number;
  messageId?: number;
  timestamp?: string;
  source: 'immediate' | 'delayed' | 'queued';
  scheduledAt?: string;
  originalDelay?: number;
}

interface TestResult {
  id: string;
  prompt: string;
  success: boolean;
  response?: any;
  error?: string;
  duration: number;
  timestamp: string;
  conversationId?: number;
  messageCount?: number;
  delayedMessages?: any[];
  // Enhanced character reply analysis
  characterReplies?: CharacterReply[];
  replyAnalysis?: {
    totalReplies: number;
    immediateReplies: number;
    delayedReplies: number;
    characterBreakdown: { [character: string]: number };
    averageDelay: number;
    totalResponseTime: number;
    theme?: string;
    skills?: string[];
  };
}

interface TestConfig {
  serverUrl?: string;
  serverPort?: number;
  maxDelayedWaitTime?: number; // Maximum time to wait for delayed thoughts
  logLevel?: 'verbose' | 'normal' | 'minimal';
  outputFile?: string;
  skipDelayWait?: boolean; // Skip waiting for delays and just capture queue info
}

export class IntegrationTestRunner {
  private app: ForaChatApp | null = null;
  private config: TestConfig;
  private results: TestResult[] = [];
  private sessionId: string;
  private sessionCookie: string | null = null;

  constructor(config: TestConfig = {}) {
    this.config = {
      serverUrl: 'localhost',
      serverPort: 3000,
      maxDelayedWaitTime: 30000, // 30 seconds
      logLevel: 'normal',
      ...config
    };
    this.sessionId = uuidv4();
  }

  /**
   * Load test prompts from a file
   */
  loadPromptsFromFile(filePath: string): TestPrompt[] {
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Prompts file not found: ${fullPath}`);
    }

    const content = fs.readFileSync(fullPath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim() && !line.startsWith('#'));
    
    return lines.map((line, index) => ({
      id: `prompt_${index + 1}`,
      prompt: line.trim(),
      timeout: 60000 // 1 minute default timeout per prompt
    }));
  }

  /**
   * Load test prompts from JSON file with more detailed configuration
   */
  loadPromptsFromJSON(filePath: string): TestPrompt[] {
    const fullPath = path.resolve(filePath);
    
    if (!fs.existsSync(fullPath)) {
      throw new Error(`Prompts JSON file not found: ${fullPath}`);
    }

    const content = fs.readFileSync(fullPath, 'utf-8');
    const data = JSON.parse(content);
    
    if (!Array.isArray(data.prompts)) {
      throw new Error('JSON file must contain a "prompts" array');
    }

    return data.prompts.map((prompt: any, index: number) => ({
      id: prompt.id || `prompt_${index + 1}`,
      prompt: prompt.text || prompt.prompt,
      expectedTheme: prompt.expectedTheme,
      expectedSkills: prompt.expectedSkills,
      timeout: prompt.timeout || 60000
    }));
  }

  /**
   * Start the ForaChat server
   */
  async startServer(): Promise<void> {
    // First check if server is already running
    try {
      await this.waitForServerReady(3); // Quick check with fewer attempts
      this.log('✅ ForaChat server is already running', 'normal');
      return;
    } catch (error) {
      this.log('🚀 Starting ForaChat server for integration testing...', 'verbose');
    }

    this.app = new ForaChatApp({ quietMode: true });
    await this.app.start();

    this.log('✅ ForaChat server started successfully', 'normal');

    // Wait for server to be fully ready
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Verify server is responding
    await this.waitForServerReady();
  }

  /**
   * Wait for server to be ready by checking health endpoint
   */
  private async waitForServerReady(maxAttempts: number = 10): Promise<void> {
    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        const response = await this.makeHttpRequest('/health', 'GET');
        if (response.status === 'healthy' || response.status === 'ok') {
          this.log('✅ Server health check passed', 'verbose');
          return;
        } else {
          throw new Error(`Unexpected health status: ${response.status}`);
        }
      } catch (error) {
        this.log(`⏳ Server health check attempt ${attempt}/${maxAttempts} failed: ${(error as Error).message}`, 'verbose');
        if (attempt === maxAttempts) {
          throw new Error(`Server failed to become ready after ${maxAttempts} attempts: ${(error as Error).message}`);
        }
        await new Promise(resolve => setTimeout(resolve, 1000));
      }
    }
  }

  /**
   * Send a message to the chat endpoint
   */
  private async sendMessage(prompt: string): Promise<any> {
    const postData = JSON.stringify({ text: prompt });
    return this.makeHttpRequest('/chat', 'POST', postData);
  }

  /**
   * Poll for delayed messages
   */
  private async pollForDelayedMessages(conversationId: number, lastMessageId: number = 0): Promise<any[]> {
    const delayedMessages: any[] = [];
    const startTime = Date.now();
    let currentLastMessageId = lastMessageId;

    while (Date.now() - startTime < this.config.maxDelayedWaitTime!) {
      try {
        const response = await this.makeHttpRequest(
          `/conversation/${conversationId}`,
          'GET'
        );

        if (response.messages && response.messages.length > 0) {
          // Filter for new messages that came after our last known message ID
          const newMessages = response.messages.filter((msg: any) =>
            msg.id > currentLastMessageId && msg.character !== 'user'
          );

          if (newMessages.length > 0) {
            delayedMessages.push(...newMessages);
            currentLastMessageId = Math.max(...newMessages.map((msg: any) => msg.id));
            this.log(`📨 Received ${newMessages.length} delayed messages`, 'verbose');
          }
        }

        // Wait before next poll
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (error) {
        this.log(`⚠️ Error polling for delayed messages: ${(error as Error).message}`, 'verbose');
        break;
      }
    }

    return delayedMessages;
  }

  /**
   * Get queued messages with delay information without waiting for delivery
   */
  private async getQueuedMessages(conversationId: number): Promise<any[]> {
    try {
      const response = await this.makeHttpRequest(
        `/conversation/${conversationId}/queue`,
        'GET'
      );

      if (response.queuedMessages && Array.isArray(response.queuedMessages)) {
        this.log(`📋 Found ${response.queuedMessages.length} queued messages with delays`, 'verbose');
        return response.queuedMessages.map((msg: any) => ({
          ...msg,
          source: 'queued',
          originalDelay: msg.delay_ms,
          scheduledAt: msg.scheduled_at
        }));
      }
    } catch (error) {
      this.log(`❌ Error getting queued messages: ${error}`, 'verbose');
    }

    return [];
  }

  /**
   * Make HTTP request to the server
   */
  private async makeHttpRequest(path: string, method: string, data?: string): Promise<any> {
    return new Promise((resolve, reject) => {
      const headers: any = {
        'Content-Type': 'application/json',
        ...(data && { 'Content-Length': Buffer.byteLength(data) })
      };

      // Include session cookie if we have one
      if (this.sessionCookie) {
        headers['Cookie'] = this.sessionCookie;
      }

      const options = {
        hostname: this.config.serverUrl,
        port: this.config.serverPort,
        path,
        method,
        headers
      };

      this.log(`🌐 Making ${method} request to http://${options.hostname}:${options.port}${path}`, 'verbose');
      if (this.sessionCookie) {
        this.log(`🍪 Using session cookie: ${this.sessionCookie.substring(0, 50)}...`, 'verbose');
      }

      const req = http.request(options, (res) => {
        let responseData = '';

        // Capture session cookie from response if we don't have one yet
        if (!this.sessionCookie && res.headers['set-cookie']) {
          const cookies = Array.isArray(res.headers['set-cookie'])
            ? res.headers['set-cookie']
            : [res.headers['set-cookie']];

          const sessionCookie = cookies.find(cookie =>
            cookie.startsWith('forachat_session=')
          );

          if (sessionCookie) {
            this.sessionCookie = sessionCookie.split(';')[0]; // Remove cookie attributes
            this.log(`🍪 Captured session cookie: ${this.sessionCookie}`, 'verbose');
          }
        }

        res.on('data', (chunk) => {
          responseData += chunk;
        });

        res.on('end', () => {
          try {
            if (responseData.trim()) {
              const parsed = JSON.parse(responseData);
              resolve(parsed);
            } else {
              resolve({});
            }
          } catch (error) {
            reject(new Error(`Failed to parse response: ${responseData}`));
          }
        });
      });

      req.on('error', (error) => {
        this.log(`❌ HTTP request error: ${error.message}`, 'verbose');
        reject(error);
      });

      if (data) {
        req.write(data);
      }
      req.end();
    });
  }

  /**
   * Run a single test prompt
   */
  async runSingleTest(testPrompt: TestPrompt): Promise<TestResult> {
    const startTime = Date.now();
    const timestamp = new Date().toISOString();
    
    this.log(`\n🧪 Running test: ${testPrompt.id}`, 'normal');
    this.log(`📝 Prompt: "${testPrompt.prompt}"`, 'normal');

    try {
      // Send the message
      const response = await Promise.race([
        this.sendMessage(testPrompt.prompt),
        new Promise((_, reject) => 
          setTimeout(() => reject(new Error('Request timeout')), testPrompt.timeout!)
        )
      ]);

      this.log(`✅ Initial response received`, 'verbose');
      this.log(`📊 Response: ${JSON.stringify(response, null, 2)}`, 'verbose');

      // Poll for delayed messages if we have a conversation ID
      let delayedMessages: any[] = [];
      if (response.conversationId) {
        if (this.config.skipDelayWait) {
          this.log(`📋 Getting queued messages without waiting...`, 'verbose');
          delayedMessages = await this.getQueuedMessages(response.conversationId);
        } else {
          this.log(`⏳ Polling for delayed messages...`, 'verbose');
          const lastMessageId = response.reply ? Math.max(...response.reply.map((msg: any) => msg.id || 0)) : 0;
          delayedMessages = await this.pollForDelayedMessages(response.conversationId, lastMessageId);
        }
      }

      const duration = Date.now() - startTime;
      const totalMessages = (response.reply?.length || 0) + delayedMessages.length;

      // Analyze character replies comprehensively
      const characterReplies = this.analyzeCharacterReplies(response, delayedMessages);
      const replyAnalysis = this.generateReplyAnalysis(characterReplies, response, duration);

      const result: TestResult = {
        id: testPrompt.id,
        prompt: testPrompt.prompt,
        success: true,
        response,
        duration,
        timestamp,
        conversationId: response.conversationId,
        messageCount: totalMessages,
        delayedMessages,
        characterReplies,
        replyAnalysis
      };

      this.log(`✅ Test completed successfully in ${duration}ms`, 'normal');
      this.log(`📈 Total messages: ${totalMessages} (${response.reply?.length || 0} immediate + ${delayedMessages.length} delayed)`, 'normal');
      this.log(`🎭 Characters: ${Object.keys(replyAnalysis.characterBreakdown).join(', ')}`, 'normal');

      return result;

    } catch (error) {
      const duration = Date.now() - startTime;
      const result: TestResult = {
        id: testPrompt.id,
        prompt: testPrompt.prompt,
        success: false,
        error: (error as Error).message,
        duration,
        timestamp
      };

      this.log(`❌ Test failed: ${(error as Error).message}`, 'normal');
      return result;
    }
  }

  /**
   * Reset session for a fresh start (optional)
   */
  private resetSession(): void {
    this.sessionCookie = null;
    this.sessionId = uuidv4();
    this.log(`🔄 Reset session, new session ID: ${this.sessionId}`, 'verbose');
  }

  /**
   * Analyze character replies from both immediate and delayed responses
   */
  private analyzeCharacterReplies(response: any, delayedMessages: any[]): CharacterReply[] {
    const characterReplies: CharacterReply[] = [];

    // Process immediate replies from the initial response
    if (response.reply && Array.isArray(response.reply)) {
      response.reply.forEach((reply: any) => {
        characterReplies.push({
          character: reply.character,
          text: reply.text,
          delay: reply.delay || 0,
          source: 'immediate',
          timestamp: new Date().toISOString()
        });
      });
    }

    // Process delayed messages
    delayedMessages.forEach((msg: any) => {
      characterReplies.push({
        character: msg.character,
        text: msg.text,
        delay: msg.delay_ms || msg.delay || 0, // Use delay_ms from queue or delay from delivered messages
        messageId: msg.id,
        source: msg.source || 'delayed',
        timestamp: msg.created_at || msg.scheduled_at || new Date().toISOString(),
        scheduledAt: msg.scheduled_at,
        originalDelay: msg.delay_ms
      });
    });

    return characterReplies;
  }

  /**
   * Generate comprehensive analysis of character replies
   */
  private generateReplyAnalysis(characterReplies: CharacterReply[], response: any, totalDuration: number): any {
    const characterBreakdown: { [character: string]: number } = {};
    let totalDelay = 0;
    let immediateReplies = 0;
    let delayedReplies = 0;

    characterReplies.forEach(reply => {
      // Count by character
      characterBreakdown[reply.character] = (characterBreakdown[reply.character] || 0) + 1;

      // Count by source
      if (reply.source === 'immediate') {
        immediateReplies++;
        totalDelay += reply.delay;
      } else {
        delayedReplies++;
      }
    });

    return {
      totalReplies: characterReplies.length,
      immediateReplies,
      delayedReplies,
      characterBreakdown,
      averageDelay: immediateReplies > 0 ? Math.round(totalDelay / immediateReplies) : 0,
      totalResponseTime: totalDuration,
      theme: response.theme || 'unknown',
      skills: response.skills || []
    };
  }

  /**
   * Run all test prompts
   */
  async runTests(prompts: TestPrompt[]): Promise<TestResult[]> {
    this.log(`\n🎯 Starting integration test run with ${prompts.length} prompts`, 'normal');
    this.log(`🔧 Session ID: ${this.sessionId}`, 'verbose');

    const results: TestResult[] = [];

    for (let i = 0; i < prompts.length; i++) {
      const prompt = prompts[i];
      this.log(`\n📋 Progress: ${i + 1}/${prompts.length}`, 'normal');

      const result = await this.runSingleTest(prompt);
      results.push(result);
      this.results.push(result);

      // Brief pause between tests
      if (i < prompts.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }

    return results;
  }

  /**
   * Generate test report
   */
  generateReport(results: TestResult[]): string {
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);
    const avgDuration = totalDuration / results.length;

    let report = `
# Integration Test Report
Generated: ${new Date().toISOString()}
Session ID: ${this.sessionId}

## Summary
- Total Tests: ${results.length}
- Successful: ${successful}
- Failed: ${failed}
- Success Rate: ${((successful / results.length) * 100).toFixed(1)}%
- Total Duration: ${(totalDuration / 1000).toFixed(2)}s
- Average Duration: ${(avgDuration / 1000).toFixed(2)}s

## Test Results
`;

    results.forEach(result => {
      report += `
### ${result.id} ${result.success ? '✅' : '❌'}
- **Prompt**: "${result.prompt}"
- **Duration**: ${(result.duration / 1000).toFixed(2)}s
- **Timestamp**: ${result.timestamp}
`;

      if (result.success) {
        report += `- **Conversation ID**: ${result.conversationId}
- **Message Count**: ${result.messageCount}
- **Theme**: ${result.response?.theme || 'N/A'}
- **Skills**: ${result.response?.skills?.join(', ') || 'N/A'}
`;

        // Add character reply analysis
        if (result.replyAnalysis) {
          const analysis = result.replyAnalysis;
          report += `
#### Character Reply Analysis
- **Total Replies**: ${analysis.totalReplies}
- **Immediate Replies**: ${analysis.immediateReplies}
- **Delayed Replies**: ${analysis.delayedReplies}
- **Average Delay**: ${analysis.averageDelay}ms
- **Character Breakdown**: ${Object.entries(analysis.characterBreakdown)
  .map(([char, count]) => `${char} (${count})`)
  .join(', ')}
`;
        }

        // Add detailed character replies
        if (result.characterReplies && result.characterReplies.length > 0) {
          report += `
#### Character Replies
`;
          result.characterReplies.forEach((reply, index) => {
            const truncatedText = reply.text.length > 100
              ? reply.text.substring(0, 100) + '...'
              : reply.text;

            report += `${index + 1}. **${reply.character}** (${reply.source}${reply.delay > 0 ? `, ${reply.delay}ms delay` : ''}): "${truncatedText}"
`;
          });
        }
      } else {
        report += `- **Error**: ${result.error}
`;
      }
    });

    return report;
  }

  /**
   * Save results to file
   */
  async saveResults(results: TestResult[], outputPath?: string): Promise<void> {
    const filePath = outputPath || this.config.outputFile || `integration-test-results-${Date.now()}.json`;
    
    const output = {
      sessionId: this.sessionId,
      timestamp: new Date().toISOString(),
      config: this.config,
      summary: {
        total: results.length,
        successful: results.filter(r => r.success).length,
        failed: results.filter(r => !r.success).length,
        totalDuration: results.reduce((sum, r) => sum + r.duration, 0)
      },
      results
    };

    fs.writeFileSync(filePath, JSON.stringify(output, null, 2));
    this.log(`💾 Results saved to: ${filePath}`, 'normal');

    // Also save markdown report
    const reportPath = filePath.replace('.json', '.md');
    const report = this.generateReport(results);
    fs.writeFileSync(reportPath, report);
    this.log(`📄 Report saved to: ${reportPath}`, 'normal');
  }

  /**
   * Cleanup resources
   */
  async cleanup(): Promise<void> {
    this.log('\n🧹 Cleaning up...', 'verbose');
    
    if (this.app) {
      // Note: ForaChatApp doesn't have a stop method, so we'll just set it to null
      // In a real implementation, you might want to add a proper shutdown method
      this.app = null;
    }
    
    this.log('✅ Cleanup completed', 'verbose');
  }

  /**
   * Log message based on configured log level
   */
  private log(message: string, level: 'verbose' | 'normal' | 'minimal' = 'normal'): void {
    const levels = { minimal: 0, normal: 1, verbose: 2 };
    const currentLevel = levels[this.config.logLevel!];
    const messageLevel = levels[level];

    if (messageLevel <= currentLevel) {
      const timestamp = new Date().toLocaleTimeString();
      console.log(`[${timestamp}] ${message}`);
    }
  }
}
