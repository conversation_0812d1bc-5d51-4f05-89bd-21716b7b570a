// Core data models and types

// Character mood types
export type ForaMood = 'enthusiastic' | 'supportive' | 'energetic' | 'thoughtful' | 'determined' | 'collaborative' | 'tired' | 'grumpy' | 'diengaged';
export type JanMood = 'analytical' | 'focused' | 'pragmatic' | 'direct' | 'methodical' | 'decisive' | 'procrastinating' | 'distracted' | 'overwhelmed';
export type LouMood = 'contemplative' | 'intuitive' | 'empathetic' | 'observant' | 'reflective' | 'grounded' | 'flighty' | 'daydreaming' | 'disconnected';

export interface CharacterMoods {
  Fora: ForaMood;
  Jan: JanMood;
  Lou: LouMood;
}

export interface Conversation {
  id: number;
  created_at: Date;
  updated_at: Date;
  theme?: string;
  skills?: string[] | string;
  last_user_activity?: Date;
  engagement_level?: number; // 0-1 scale, starts at 1, decays over time
  character_moods?: CharacterMoods | string;
}

export interface Message {
  id: number;
  character: string;
  text: string;
  conversation_id: number;
  created_at: Date;
  updated_at: Date;
}

export interface ChatMessage {
  character: string;
  text: string;
  delay: number;
}

export interface DelayedMessage {
  id?: number;
  character: string;
  text: string;
  delay?: number;
  created_at?: Date;
}

export interface ChatResponse {
  reply: ChatMessage[];
  skills: string[];
  theme: string;
}

export interface UserRequest {
  text: string;
  userId?: string;
  sessionId?: string;
}

export interface Session {
  id: string;
  user_identifier: string;
  channel: 'web' | 'repl' | 'sms' | 'slack' | 'teams';
  conversation_id?: number;
  created_at: Date;
  updated_at: Date;
  last_activity: Date;
  expires_at?: Date;
  metadata?: Record<string, any>;
}

export interface SessionCreateRequest {
  userIdentifier: string;
  channel: 'web' | 'repl' | 'sms' | 'slack' | 'teams';
  conversationId?: number;
  metadata?: Record<string, any>;
  expiresAt?: Date;
}

export interface ChatResult {
  response: ChatResponse;
  conversationId: number;
  messageId: number;
}

// Interface for different messaging platforms
export interface MessageInterface {
  sendMessage(message: string): Promise<void>;
  receiveMessage(): Promise<string>;
  formatResponse(response: ChatResponse): string;
}

// Configuration types
export interface DatabaseConfig {
  url: string;
  client: string;
}

export interface LLMConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
}

export interface AppConfig {
  database: DatabaseConfig;
  llm: LLMConfig;
  port: number;
  environment: 'development' | 'production' | 'test';
}

// Message Queue types
export type MessageQueueStatus = 'PENDING' | 'PROCESSING' | 'VALIDATING' | 'SENT' | 'CANCELLED' | 'WITHDRAWN';
export type ValidationStatus = 'NOT_VALIDATED' | 'VALIDATING' | 'VALIDATED';
export type ValidationDecision = 'SEND_AS_IS' | 'WITHDRAW' | 'REVISE';

export interface QueuedMessage {
  id: number;
  conversation_id: number;
  character: string;
  text: string;
  delay_ms: number;
  status: MessageQueueStatus;
  priority: number;
  similarity_hash?: string;
  similarity_score?: number;
  scheduled_at?: Date;
  created_at: Date;
  updated_at: Date;
  // Validation fields
  validation_status?: ValidationStatus;
  validation_decision?: ValidationDecision;
  original_text?: string;
  validation_attempts?: number;
  validation_started_at?: Date;
  validation_completed_at?: Date;
  validation_reasoning?: string;
}

export interface QueueMessageInput {
  conversation_id: number;
  character: string;
  text: string;
  delay_ms?: number;
  priority?: number;
  scheduled_at?: Date;
}

export interface SimilarityCheck {
  isDuplicate: boolean;
  isSimilar: boolean;
  similarityScore: number;
  existingMessage?: QueuedMessage;
}

// Message Validation types
export interface MessageValidationInput {
  messageId: number;
  character: string;
  originalText: string;
  conversationId: number;
  conversationHistory: string;
  characterMood?: string;
}

export interface MessageValidationResult {
  decision: ValidationDecision;
  reasoning: string;
  revisedText?: string;
}

export interface ValidationContext {
  originalMessage: QueuedMessage;
  conversationHistory: Message[];
  characterMoods: CharacterMoods | null;
}
