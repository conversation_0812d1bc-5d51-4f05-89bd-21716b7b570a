import { DBOS } from '@dbos-inc/dbos-sdk';
import * as fs from 'fs/promises';
import * as path from 'path';
import { logger } from '../utils/Logger';

export class PromptService {
  private static promptCache: Map<string, string> = new Map();

  static async getSystemPrompt(promptName: string = 'agent_system'): Promise<string> {
    // Check cache first
    if (PromptService.promptCache.has(promptName)) {
      return PromptService.promptCache.get(promptName)!;
    }

    const filePath = path.join(__dirname, '..', '..', 'prompts', `${promptName}.md`);

    try {
      const content = await fs.readFile(filePath, 'utf-8');

      // Resolve embedded markdown links
      const resolvedContent = await PromptService.resolveMarkdownLinks(content);

      // Cache the resolved prompt for future use
      PromptService.promptCache.set(promptName, resolvedContent);

      return resolvedContent;
    } catch (error) {
      logger.error(`Failed to read prompt file ${filePath}`, error);
      throw new Error(`Prompt file not found: ${promptName}`);
    }
  }

  @DBOS.step()
  static async getAvailablePrompts(): Promise<string[]> {
    const promptsDir = path.join(__dirname, '..', '..', 'prompts');
    
    try {
      const files = await fs.readdir(promptsDir);
      return files
        .filter(file => file.endsWith('.md'))
        .map(file => file.replace('.md', ''));
    } catch (error) {
      logger.error(`Failed to read prompts directory`, error);
      return [];
    }
  }

  static clearCache(): void {
    PromptService.promptCache.clear();
  }

  static getCacheSize(): number {
    return PromptService.promptCache.size;
  }

  /**
   * Resolves embedded markdown links in the format [Link Text](filename.md)
   * by reading the referenced files and replacing the links with their content
   * Recursively resolves nested links in referenced files
   */
  private static async resolveMarkdownLinks(content: string, processedFiles: Set<string> = new Set()): Promise<string> {
    // Regular expression to match markdown links: [text](filename.md)
    const linkRegex = /\[([^\]]+)\]\(([^)]+\.md)\)/g;
    let resolvedContent = content;
    const matches = Array.from(content.matchAll(linkRegex));

    for (const match of matches) {
      const [fullMatch, linkText, filename] = match;

      // Prevent infinite recursion by tracking processed files
      if (processedFiles.has(filename)) {
        logger.warn(`Circular reference detected for ${filename}, skipping`);
        continue;
      }

      try {
        // Construct the full path to the referenced file
        const referencedFilePath = path.join(__dirname, '..', '..', 'prompts', filename);

        // Read the referenced file
        const referencedContent = await fs.readFile(referencedFilePath, 'utf-8');

        // Add this file to processed set to prevent circular references
        const newProcessedFiles = new Set(processedFiles);
        newProcessedFiles.add(filename);

        // Recursively resolve links in the referenced content
        const resolvedReferencedContent = await PromptService.resolveMarkdownLinks(referencedContent, newProcessedFiles);

        // Replace the link with the file content
        // Add a header with the link text for context
        const replacement = `\n\n### ${linkText}\n\n${resolvedReferencedContent}\n`;
        resolvedContent = resolvedContent.replace(fullMatch, replacement);

        logger.info(`Resolved markdown link: ${linkText} -> ${filename}`);
      } catch (error) {
        logger.warn(`Failed to resolve markdown link ${filename}`, error);
        // Keep the original link if we can't resolve it
      }
    }

    return resolvedContent;
  }
}
