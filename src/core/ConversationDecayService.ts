import { DBOS } from '@dbos-inc/dbos-sdk';
import { ConversationService } from './ConversationService';
import { logger } from '../utils/Logger';

export interface DecayConfig {
  // Time thresholds in milliseconds
  initialDecayThreshold: number;    // When decay starts (default: 2 minutes)
  maxDecayThreshold: number;        // When conversation times out completely (default: 15 minutes)
  
  // Decay parameters
  minDelayMultiplier: number;       // Minimum delay multiplier (default: 1.0 - no change)
  maxDelayMultiplier: number;       // Maximum delay multiplier (default: 8.0 - 8x slower)
  decayExponent: number;            // Exponential decay rate (default: 1.5)
  
  // Engagement thresholds
  minEngagementLevel: number;       // Below this, conversation times out (default: 0.1)
}

export class ConversationDecayService {
  private static readonly DEFAULT_CONFIG: DecayConfig = {
    initialDecayThreshold: 2 * 60 * 1000,      // 2 minutes
    maxDecayThreshold: 15 * 60 * 1000,         // 15 minutes
    minDelayMultiplier: 1.0,
    maxDelayMultiplier: 8.0,
    decayExponent: 1.5,
    minEngagementLevel: 0.1
  };

  /**
   * Calculate the delay multiplier based on time since last user activity
   * Returns a multiplier that increases delays as user engagement decreases
   */
  static calculateDelayMultiplier(
    timeSinceLastActivity: number,
    config: Partial<DecayConfig> = {}
  ): number {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };
    
    // No decay if within initial threshold
    if (timeSinceLastActivity <= cfg.initialDecayThreshold) {
      return cfg.minDelayMultiplier;
    }
    
    // Complete timeout if beyond max threshold
    if (timeSinceLastActivity >= cfg.maxDecayThreshold) {
      return Infinity; // Indicates conversation should timeout
    }
    
    // Calculate decay progress (0 to 1)
    const decayProgress = (timeSinceLastActivity - cfg.initialDecayThreshold) / 
                         (cfg.maxDecayThreshold - cfg.initialDecayThreshold);
    
    // Apply exponential decay
    const exponentialDecay = Math.pow(decayProgress, cfg.decayExponent);
    
    // Calculate multiplier
    const multiplier = cfg.minDelayMultiplier + 
                      (cfg.maxDelayMultiplier - cfg.minDelayMultiplier) * exponentialDecay;
    
    return Math.min(multiplier, cfg.maxDelayMultiplier);
  }

  /**
   * Calculate engagement level based on time since last activity
   * Returns a value between 0 and 1, where 1 is fully engaged and 0 is completely disengaged
   */
  static calculateEngagementLevel(
    timeSinceLastActivity: number,
    config: Partial<DecayConfig> = {}
  ): number {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };
    
    // Full engagement if within initial threshold
    if (timeSinceLastActivity <= cfg.initialDecayThreshold) {
      return 1.0;
    }
    
    // No engagement if beyond max threshold
    if (timeSinceLastActivity >= cfg.maxDecayThreshold) {
      return 0.0;
    }
    
    // Calculate linear decay from 1.0 to 0.0
    const decayProgress = (timeSinceLastActivity - cfg.initialDecayThreshold) / 
                         (cfg.maxDecayThreshold - cfg.initialDecayThreshold);
    
    return Math.max(0.0, 1.0 - decayProgress);
  }

  /**
   * Check if a conversation should timeout based on engagement level
   */
  static shouldTimeoutConversation(
    engagementLevel: number,
    config: Partial<DecayConfig> = {}
  ): boolean {
    const cfg = { ...this.DEFAULT_CONFIG, ...config };
    return engagementLevel <= cfg.minEngagementLevel;
  }

  /**
   * Update conversation engagement level based on current activity
   */
  @DBOS.workflow()
  static async updateConversationEngagement(
    conversationId: number,
    config: Partial<DecayConfig> = {}
  ): Promise<{ engagementLevel: number; shouldTimeout: boolean; delayMultiplier: number }> {
    const lastActivity = await ConversationService.getLastUserActivity(conversationId);

    if (!lastActivity) {
      // No activity recorded, assume conversation just started
      await ConversationService.updateUserActivity(conversationId);
      return {
        engagementLevel: 1.0,
        shouldTimeout: false,
        delayMultiplier: 1.0
      };
    }

    const timeSinceLastActivity = Date.now() - lastActivity.getTime();
    const engagementLevel = ConversationDecayService.calculateEngagementLevel(timeSinceLastActivity, config);
    const delayMultiplier = ConversationDecayService.calculateDelayMultiplier(timeSinceLastActivity, config);
    const shouldTimeout = ConversationDecayService.shouldTimeoutConversation(engagementLevel, config);

    // Update engagement level in database
    await ConversationService.updateEngagementLevel(conversationId, engagementLevel);

    // Log detailed decay information
    const minutes = Math.round(timeSinceLastActivity / 60000);
    const seconds = Math.round((timeSinceLastActivity % 60000) / 1000);

    logger.info(`Conversation ${conversationId} decay analysis: ` +
      `timeSinceLastActivity=${minutes}m ${seconds}s, ` +
      `engagementLevel=${Math.round(engagementLevel * 100)}%, ` +
      `delayMultiplier=${Math.round(delayMultiplier * 10) / 10}x, ` +
      `shouldTimeout=${shouldTimeout}, ` +
      `status=${ConversationDecayService.getDecayStatus(timeSinceLastActivity, engagementLevel, delayMultiplier)}`);

    return {
      engagementLevel,
      shouldTimeout,
      delayMultiplier
    };
  }

  /**
   * Apply decay to a base delay value
   */
  static applyDecayToDelay(
    baseDelay: number,
    delayMultiplier: number
  ): number {
    if (delayMultiplier === Infinity) {
      return Infinity; // Indicates no message should be sent
    }
    
    return Math.round(baseDelay * delayMultiplier);
  }

  /**
   * Get human-readable decay status for logging
   */
  static getDecayStatus(
    timeSinceLastActivity: number,
    engagementLevel: number,
    delayMultiplier: number
  ): string {
    const minutes = Math.round(timeSinceLastActivity / 60000);
    const engagement = Math.round(engagementLevel * 100);
    const multiplier = Math.round(delayMultiplier * 10) / 10;
    
    if (delayMultiplier === Infinity) {
      return `TIMEOUT (${minutes}m inactive, ${engagement}% engaged)`;
    } else if (delayMultiplier > 4) {
      return `HEAVY_DECAY (${minutes}m inactive, ${engagement}% engaged, ${multiplier}x slower)`;
    } else if (delayMultiplier > 2) {
      return `MODERATE_DECAY (${minutes}m inactive, ${engagement}% engaged, ${multiplier}x slower)`;
    } else if (delayMultiplier > 1) {
      return `LIGHT_DECAY (${minutes}m inactive, ${engagement}% engaged, ${multiplier}x slower)`;
    } else {
      return `ACTIVE (${minutes}m inactive, ${engagement}% engaged, normal speed)`;
    }
  }
}
