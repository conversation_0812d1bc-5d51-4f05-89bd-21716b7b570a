import { DBOS } from '@dbos-inc/dbos-sdk';
import { Session, SessionCreateRequest } from '../models/types';
import { logger } from '../utils/Logger';
import { v4 as uuidv4 } from 'uuid';

export class SessionService {
  @DBOS.transaction()
  static async createSession(request: SessionCreateRequest): Promise<Session> {
    // Check if there's a recent session for this user to prevent rapid session creation
    const recentSession = await DBOS.knexClient<Session>('forachat.sessions')
      .where('user_identifier', request.userIdentifier)
      .where('channel', request.channel)
      .where('created_at', '>', new Date(Date.now() - 30 * 1000)) // Within last 30 seconds
      .where('expires_at', '>', new Date()) // Not expired
      .orderBy('created_at', 'desc')
      .first();

    if (recentSession) {
      logger.info(`Reusing recent session ${recentSession.id} for user ${request.userIdentifier} (created ${Math.round((Date.now() - new Date(recentSession.created_at).getTime()) / 1000)}s ago)`);

      // Parse metadata if it exists
      if (recentSession.metadata && typeof recentSession.metadata === 'string') {
        recentSession.metadata = JSON.parse(recentSession.metadata as string);
      }

      return recentSession;
    }

    const sessionId = uuidv4();
    const now = new Date();
    const expiresAt = request.expiresAt || new Date(now.getTime() + 7 * 24 * 60 * 60 * 1000); // Default 7 days

    try {
      await DBOS.knexClient<Session>('forachat.sessions')
        .insert({
          id: sessionId,
          user_identifier: request.userIdentifier,
          channel: request.channel,
          conversation_id: request.conversationId,
          last_activity: now,
          expires_at: expiresAt,
          metadata: request.metadata ? JSON.stringify(request.metadata) as any : undefined
        });

      // Retrieve the created session
      const session = await DBOS.knexClient<Session>('forachat.sessions')
        .where('id', sessionId)
        .first();

      if (!session) {
        logger.error(`Failed to retrieve created session ${sessionId}`);
        throw new Error(`Failed to create session ${sessionId}`);
      }

      // Parse metadata if it exists
      if (session.metadata && typeof session.metadata === 'string') {
        session.metadata = JSON.parse(session.metadata as string);
      }

      logger.info(`Created new session ${sessionId} for user ${request.userIdentifier} on ${request.channel}`);
      return session;
    } catch (error) {
      logger.error(`Error creating session`, error);
      throw error;
    }
  }

  @DBOS.transaction()
  static async getSession(sessionId: string): Promise<Session | null> {
    const session = await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .first();

    if (!session) {
      logger.debug(`Session ${sessionId} not found in database`);
      return null;
    }

    // Check if session has expired
    if (session.expires_at && session.expires_at < new Date()) {
      logger.debug(`Session ${sessionId} has expired at ${session.expires_at}`);
      return null;
    }

    // Parse metadata if it exists
    if (session.metadata && typeof session.metadata === 'string') {
      session.metadata = JSON.parse(session.metadata as string);
    }

    logger.debug(`Retrieved valid session ${sessionId} for user ${session.user_identifier}`);
    return session;
  }

  @DBOS.transaction()
  static async getSessionByUserAndChannel(
    userIdentifier: string, 
    channel: string
  ): Promise<Session | null> {
    const session = await DBOS.knexClient<Session>('forachat.sessions')
      .where('user_identifier', userIdentifier)
      .where('channel', channel)
      .where('expires_at', '>', new Date())
      .orderBy('last_activity', 'desc')
      .first();

    if (!session) {
      return null;
    }

    // Parse metadata if it exists
    if (session.metadata && typeof session.metadata === 'string') {
      session.metadata = JSON.parse(session.metadata as string);
    }

    return session;
  }

  @DBOS.transaction()
  static async updateSessionActivity(sessionId: string): Promise<void> {
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .update({
        last_activity: new Date(),
        updated_at: new Date()
      });
  }

  @DBOS.transaction()
  static async updateSessionConversation(
    sessionId: string, 
    conversationId: number
  ): Promise<void> {
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .update({
        conversation_id: conversationId,
        updated_at: new Date()
      });

    logger.info(`Updated session ${sessionId} with conversation ${conversationId}`);
  }

  @DBOS.transaction()
  static async updateSessionMetadata(
    sessionId: string,
    metadata: Record<string, any>
  ): Promise<void> {
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .update({
        metadata: JSON.stringify(metadata) as any,
        updated_at: new Date()
      });
  }

  @DBOS.transaction()
  static async deleteSession(sessionId: string): Promise<void> {
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .del();

    logger.info(`Deleted session ${sessionId}`);
  }

  @DBOS.transaction()
  static async cleanupExpiredSessions(): Promise<number> {
    const deletedCount = await DBOS.knexClient<Session>('forachat.sessions')
      .where('expires_at', '<', new Date())
      .del();

    if (deletedCount > 0) {
      logger.info(`Cleaned up ${deletedCount} expired sessions`);
    }

    return deletedCount;
  }

  @DBOS.transaction()
  static async getSessionsByUser(userIdentifier: string): Promise<Session[]> {
    const sessions = await DBOS.knexClient<Session>('forachat.sessions')
      .where('user_identifier', userIdentifier)
      .where('expires_at', '>', new Date())
      .orderBy('last_activity', 'desc');

    // Parse metadata for each session
    return sessions.map(session => {
      if (session.metadata && typeof session.metadata === 'string') {
        session.metadata = JSON.parse(session.metadata as string);
      }
      return session;
    });
  }

  @DBOS.transaction()
  static async extendSession(sessionId: string, additionalHours: number = 24): Promise<void> {
    const session = await this.getSession(sessionId);
    if (!session) {
      throw new Error(`Session ${sessionId} not found`);
    }

    const newExpiresAt = new Date(Date.now() + additionalHours * 60 * 60 * 1000);
    
    await DBOS.knexClient<Session>('forachat.sessions')
      .where('id', sessionId)
      .update({
        expires_at: newExpiresAt,
        updated_at: new Date()
      });

    logger.info(`Extended session ${sessionId} until ${newExpiresAt}`);
  }

  // Helper method to generate session identifiers for different channels
  static generateUserIdentifier(channel: string, identifier: string): string {
    switch (channel) {
      case 'web':
        return `web_${identifier}`; // Cookie value or browser fingerprint
      case 'repl':
        return `repl_${identifier}`; // Machine hostname + user or custom ID
      case 'sms':
        return `sms_${identifier}`; // Phone number
      case 'slack':
        return `slack_${identifier}`; // Slack user ID
      case 'teams':
        return `teams_${identifier}`; // Teams user ID
      default:
        return identifier;
    }
  }
}
