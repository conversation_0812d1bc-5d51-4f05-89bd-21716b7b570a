import { DBOS } from '@dbos-inc/dbos-sdk';
import crypto from 'crypto';
import { QueuedMessage, QueueMessageInput, SimilarityCheck, MessageQueueStatus } from '../models/types';
import { logger } from '../utils/Logger';

export class MessageQueueService {
  
  /**
   * Add a message to the queue with duplicate and similarity checking
   */
  @DBOS.transaction()
  static async enqueueMessage(input: QueueMessageInput): Promise<QueuedMessage | null> {
    // Check for duplicates and similar messages first (inline to avoid nested transactions)
    const similarityHash = MessageQueueService.generateSimilarityHash(input.text);

    // First check for exact duplicates using hash
    const duplicateMessage = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', input.conversation_id)
      .where('character', input.character)
      .where('similarity_hash', similarityHash)
      .where('status', 'PENDING')
      .first();

    if (duplicateMessage) {
      logger.info(`Skipping duplicate message from ${input.character}: ${input.text.substring(0, 50)}...`);
      return null;
    }

    // Check for similar messages from the same character in the last 5 minutes
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const recentMessages = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', input.conversation_id)
      .where('character', input.character)
      .where('status', 'PENDING')
      .where('created_at', '>', fiveMinutesAgo);

    // Calculate similarity with recent messages
    const textWords = MessageQueueService.getSignificantWords(input.text);
    let maxSimilarity = 0;

    for (const message of recentMessages) {
      const messageWords = MessageQueueService.getSignificantWords(message.text);
      const similarity = MessageQueueService.calculateSimilarity(textWords, messageWords);

      if (similarity > maxSimilarity) {
        maxSimilarity = similarity;
      }
    }

    const SIMILARITY_THRESHOLD = 0.4; // 40% similarity threshold
    if (maxSimilarity > SIMILARITY_THRESHOLD) {
      logger.info(`Skipping similar message from ${input.character} (similarity: ${maxSimilarity.toFixed(3)}): ${input.text.substring(0, 50)}...`);
      return null;
    }

    // Calculate priority based on delay (shorter delay = higher priority)
    const priority = input.priority || Math.max(1, Math.floor((input.delay_ms || 3000) / 100));

    // Calculate scheduled time
    const scheduledAt = input.scheduled_at || new Date(Date.now() + (input.delay_ms || 3000));

    const [queuedMessage] = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .insert({
        conversation_id: input.conversation_id,
        character: input.character,
        text: input.text,
        delay_ms: input.delay_ms || 3000,
        priority,
        similarity_hash: similarityHash,
        scheduled_at: scheduledAt,
        status: 'PENDING'
      })
      .returning('*');

    logger.info(`Enqueued message from ${input.character} (priority: ${priority}, delay: ${input.delay_ms || 3000}ms): ${input.text.substring(0, 50)}...`);
    return queuedMessage;
  }
  
  /**
   * Get pending messages for a conversation, ordered by priority and scheduled time
   */
  @DBOS.transaction()
  static async getPendingMessages(conversationId: number, limit?: number): Promise<QueuedMessage[]> {
    let query = DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .where('status', 'PENDING')
      .orderBy('priority', 'asc')
      .orderBy('scheduled_at', 'asc');
    
    if (limit) {
      query = query.limit(limit);
    }
    
    return await query;
  }
  
  /**
   * Get messages ready to be sent (scheduled time has passed)
   */
  @DBOS.transaction()
  static async getReadyMessages(conversationId: number): Promise<QueuedMessage[]> {
    const now = new Date();
    return await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .where('status', 'PENDING')
      .where('scheduled_at', '<=', now)
      .orderBy('priority', 'asc')
      .orderBy('scheduled_at', 'asc');
  }

  /**
   * Get messages that need validation (ready to be sent but not yet validated)
   */
  @DBOS.transaction()
  static async getMessagesNeedingValidation(conversationId: number): Promise<QueuedMessage[]> {
    const now = new Date();
    return await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .where('status', 'PENDING')
      .where('scheduled_at', '<=', now)
      .where('validation_status', 'NOT_VALIDATED')
      .orderBy('priority', 'asc')
      .orderBy('scheduled_at', 'asc');
  }

  /**
   * Get validated messages ready to be sent
   */
  @DBOS.transaction()
  static async getValidatedReadyMessages(conversationId: number): Promise<QueuedMessage[]> {
    const now = new Date();
    return await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .where('status', 'PENDING')
      .where('scheduled_at', '<=', now)
      .where('validation_status', 'VALIDATED')
      .where('validation_decision', '!=', 'WITHDRAW')
      .orderBy('priority', 'asc')
      .orderBy('scheduled_at', 'asc');
  }
  
  /**
   * Update message status
   */
  @DBOS.transaction()
  static async updateMessageStatus(messageId: number, status: MessageQueueStatus): Promise<void> {
    await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('id', messageId)
      .update({
        status,
        updated_at: new Date()
      });
  }

  /**
   * Get a message by ID
   */
  @DBOS.transaction()
  static async getMessageById(messageId: number): Promise<QueuedMessage | null> {
    const message = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('id', messageId)
      .first();

    return message || null;
  }

  /**
   * Update validation status
   */
  @DBOS.transaction()
  static async updateValidationStatus(messageId: number, validationStatus: string): Promise<void> {
    const updateData: any = {
      validation_status: validationStatus,
      updated_at: new Date()
    };

    // Set validation started timestamp when starting validation
    if (validationStatus === 'VALIDATING') {
      updateData.validation_started_at = new Date();
      updateData.validation_attempts = DBOS.knexClient.raw('COALESCE(validation_attempts, 0) + 1');
    }

    await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('id', messageId)
      .update(updateData);
  }
  
  /**
   * Update message text (for reframing)
   */
  @DBOS.transaction()
  static async updateMessageText(messageId: number, newText: string): Promise<void> {
    const newSimilarityHash = MessageQueueService.generateSimilarityHash(newText);
    await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('id', messageId)
      .update({
        text: newText,
        similarity_hash: newSimilarityHash,
        updated_at: new Date()
      });
  }
  
  /**
   * Cancel pending messages (e.g., when user interrupts)
   */
  @DBOS.transaction()
  static async cancelPendingMessages(conversationId: number, characterFilter?: string): Promise<number> {
    let query = DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .where('status', 'PENDING');
    
    if (characterFilter) {
      query = query.where('character', characterFilter);
    }
    
    const cancelledCount = await query.update({
      status: 'CANCELLED',
      updated_at: new Date()
    });
    
    logger.info(`Cancelled ${cancelledCount} pending messages for conversation ${conversationId}${characterFilter ? ` from ${characterFilter}` : ''}`);
    return cancelledCount;
  }
  
  /**
   * Check for duplicate or similar messages
   */
  @DBOS.transaction()
  static async checkSimilarity(conversationId: number, character: string, text: string): Promise<SimilarityCheck> {
    const similarityHash = MessageQueueService.generateSimilarityHash(text);
    
    // First check for exact duplicates using hash
    const duplicateMessage = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .where('character', character)
      .where('similarity_hash', similarityHash)
      .where('status', 'PENDING')
      .first();
    
    if (duplicateMessage) {
      return {
        isDuplicate: true,
        isSimilar: true,
        similarityScore: 1.0,
        existingMessage: duplicateMessage
      };
    }
    
    // Check for similar messages from the same character in the last 5 minutes
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);
    const recentMessages = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .where('character', character)
      .where('status', 'PENDING')
      .where('created_at', '>', fiveMinutesAgo);
    
    // Calculate similarity with recent messages
    const textWords = MessageQueueService.getSignificantWords(text);
    let maxSimilarity = 0;
    let mostSimilarMessage: QueuedMessage | undefined;

    for (const message of recentMessages) {
      const messageWords = MessageQueueService.getSignificantWords(message.text);
      const similarity = MessageQueueService.calculateSimilarity(textWords, messageWords);
      
      if (similarity > maxSimilarity) {
        maxSimilarity = similarity;
        mostSimilarMessage = message;
      }
    }
    
    const SIMILARITY_THRESHOLD = 0.4; // 40% similarity threshold
    const isSimilar = maxSimilarity > SIMILARITY_THRESHOLD;
    
    return {
      isDuplicate: false,
      isSimilar,
      similarityScore: maxSimilarity,
      existingMessage: mostSimilarMessage
    };
  }
  
  /**
   * Generate a hash for similarity comparison
   */
  private static generateSimilarityHash(text: string): string {
    // Normalize text for hashing (lowercase, remove punctuation, sort words)
    const normalizedText = text
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2)
      .sort()
      .join(' ');
    
    return crypto.createHash('md5').update(normalizedText).digest('hex');
  }
  
  /**
   * Extract significant words from text (filter out common words)
   */
  private static getSignificantWords(text: string): string[] {
    const commonWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they', 'me', 'him', 'her', 'us', 'them']);
    
    return text
      .toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 2 && !commonWords.has(word));
  }
  
  /**
   * Calculate similarity between two sets of words
   */
  private static calculateSimilarity(words1: string[], words2: string[]): number {
    if (words1.length === 0 && words2.length === 0) return 1.0;
    if (words1.length === 0 || words2.length === 0) return 0.0;
    
    const set1 = new Set(words1);
    const set2 = new Set(words2);
    const intersection = new Set([...set1].filter(word => set2.has(word)));
    const union = new Set([...set1, ...set2]);
    
    return intersection.size / union.size;
  }
  
  /**
   * Clean up old messages (sent/cancelled messages older than 24 hours)
   */
  @DBOS.transaction()
  static async cleanupOldMessages(): Promise<number> {
    const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000);

    const deletedCount = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .whereIn('status', ['SENT', 'CANCELLED'])
      .where('updated_at', '<', oneDayAgo)
      .del();

    if (deletedCount > 0) {
      logger.info(`Cleaned up ${deletedCount} old queue messages`);
    }

    return deletedCount;
  }

  /**
   * Get queue statistics for monitoring
   */
  @DBOS.transaction()
  static async getQueueStats(conversationId?: number): Promise<{
    pending: number;
    processing: number;
    sent: number;
    cancelled: number;
    total: number;
    oldestPending?: Date;
    newestPending?: Date;
  }> {
    let baseQuery = DBOS.knexClient<QueuedMessage>('forachat.message_queue');

    if (conversationId) {
      baseQuery = baseQuery.where('conversation_id', conversationId);
    }

    const stats = await baseQuery
      .select('status')
      .count('* as count')
      .groupBy('status');

    const result = {
      pending: 0,
      processing: 0,
      sent: 0,
      cancelled: 0,
      total: 0,
      oldestPending: undefined as Date | undefined,
      newestPending: undefined as Date | undefined
    };

    for (const stat of stats) {
      const count = Number((stat as any).count);
      result.total += count;

      switch (stat.status) {
        case 'PENDING':
          result.pending = count;
          break;
        case 'PROCESSING':
          result.processing = count;
          break;
        case 'SENT':
          result.sent = count;
          break;
        case 'CANCELLED':
          result.cancelled = count;
          break;
      }
    }

    // Get oldest and newest pending messages
    if (result.pending > 0) {
      let oldestQuery = DBOS.knexClient<QueuedMessage>('forachat.message_queue')
        .where('status', 'PENDING');

      let newestQuery = DBOS.knexClient<QueuedMessage>('forachat.message_queue')
        .where('status', 'PENDING');

      if (conversationId) {
        oldestQuery = oldestQuery.where('conversation_id', conversationId);
        newestQuery = newestQuery.where('conversation_id', conversationId);
      }

      const oldestPending = await oldestQuery
        .orderBy('created_at', 'asc')
        .first();

      const newestPending = await newestQuery
        .orderBy('created_at', 'desc')
        .first();

      result.oldestPending = oldestPending?.created_at;
      result.newestPending = newestPending?.created_at;
    }

    return result;
  }

  /**
   * Reset stuck processing messages (processing for more than 5 minutes)
   */
  @DBOS.transaction()
  static async resetStuckMessages(): Promise<number> {
    const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000);

    const resetCount = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('status', 'PROCESSING')
      .where('updated_at', '<', fiveMinutesAgo)
      .update({
        status: 'PENDING',
        updated_at: new Date()
      });

    if (resetCount > 0) {
      logger.warn(`Reset ${resetCount} stuck processing messages back to pending`);
    }

    return resetCount;
  }

  /**
   * Get detailed queue information for debugging
   */
  @DBOS.transaction()
  static async getQueueDebugInfo(conversationId: number): Promise<{
    pendingMessages: QueuedMessage[];
    processingMessages: QueuedMessage[];
    recentSentMessages: QueuedMessage[];
    stats: any;
  }> {
    const [pendingMessages, processingMessages, recentSentMessages] = await Promise.all([
      // Pending messages
      DBOS.knexClient<QueuedMessage>('forachat.message_queue')
        .where('conversation_id', conversationId)
        .where('status', 'PENDING')
        .orderBy('priority', 'asc')
        .orderBy('scheduled_at', 'asc'),

      // Processing messages
      DBOS.knexClient<QueuedMessage>('forachat.message_queue')
        .where('conversation_id', conversationId)
        .where('status', 'PROCESSING')
        .orderBy('updated_at', 'desc'),

      // Recent sent messages (last 10)
      DBOS.knexClient<QueuedMessage>('forachat.message_queue')
        .where('conversation_id', conversationId)
        .where('status', 'SENT')
        .orderBy('updated_at', 'desc')
        .limit(10)
    ]);

    // Calculate stats inline to avoid nested transaction
    const statsQuery = await DBOS.knexClient<QueuedMessage>('forachat.message_queue')
      .where('conversation_id', conversationId)
      .select('status')
      .count('* as count')
      .groupBy('status');

    const stats = {
      pending: 0,
      processing: 0,
      sent: 0,
      cancelled: 0,
      total: 0
    };

    for (const stat of statsQuery) {
      const count = Number((stat as any).count);
      stats.total += count;

      switch (stat.status) {
        case 'PENDING':
          stats.pending = count;
          break;
        case 'PROCESSING':
          stats.processing = count;
          break;
        case 'SENT':
          stats.sent = count;
          break;
        case 'CANCELLED':
          stats.cancelled = count;
          break;
      }
    }

    return {
      pendingMessages,
      processingMessages,
      recentSentMessages,
      stats
    };
  }

  /**
   * Force process all ready messages (utility for debugging)
   */
  @DBOS.transaction()
  static async forceProcessReadyMessages(conversationId: number): Promise<QueuedMessage[]> {
    const readyMessages = await MessageQueueService.getReadyMessages(conversationId);

    for (const message of readyMessages) {
      await MessageQueueService.updateMessageStatus(message.id, 'PROCESSING');
    }

    logger.info(`Force processed ${readyMessages.length} ready messages for conversation ${conversationId}`);
    return readyMessages;
  }
}
