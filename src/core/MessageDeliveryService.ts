import { DBOS } from '@dbos-inc/dbos-sdk';
import knex from 'knex';
import { QueuedMessage } from '../models/types';
import { MessageQueueService } from './MessageQueueService';
import { ConversationService } from './ConversationService';
import { EventDrivenMessageProcessor } from '../streaming/EventDrivenMessageProcessor';
import { logger } from '../utils/Logger';

const config = require('../../knexfile.cjs');

/**
 * Centralized service for processing and delivering queued messages
 * Handles validation, timing, and delivery to all connected clients
 */
export class MessageDeliveryService {
  private static eventProcessor = EventDrivenMessageProcessor.getInstance();
  private static deliveryCallbacks = new Map<number, Array<(message: any) => void>>();
  private static db = knex(config.development);

  /**
   * Start event-driven processing for a conversation
   */
  static startProcessing(conversationId: number): void {
    logger.info(`🚀 Starting event-driven message delivery service for conversation ${conversationId}`);

    // Start event-driven processing
    this.eventProcessor.startConversationProcessing(conversationId);

    // Trigger processing of any existing messages that need attention
    this.processExistingMessages(conversationId);
  }

  /**
   * Stop event-driven processing for a conversation
   */
  static stopProcessing(conversationId: number): void {
    this.eventProcessor.stopConversationProcessing(conversationId);
    logger.info(`⏹️ Stopped event-driven message delivery service for conversation ${conversationId}`);
  }

  /**
   * Process any existing messages that need attention
   */
  private static async processExistingMessages(conversationId: number): Promise<void> {
    try {
      // Check for messages needing validation
      const messagesNeedingValidation = await MessageQueueService.getMessagesNeedingValidation(conversationId);

      for (const message of messagesNeedingValidation) {
        this.eventProcessor.emitMessageEvent({
          type: 'queued',
          messageId: message.id,
          conversationId: message.conversation_id,
          character: message.character,
          timestamp: new Date()
        });
      }

      // Check for validated messages ready to be sent
      const readyMessages = await MessageQueueService.getValidatedReadyMessages(conversationId);

      for (const message of readyMessages) {
        this.eventProcessor.emitMessageEvent({
          type: 'ready',
          messageId: message.id,
          conversationId: message.conversation_id,
          character: message.character,
          timestamp: new Date()
        });
      }

      logger.info(`🔄 Triggered processing for ${messagesNeedingValidation.length} validation-pending and ${readyMessages.length} ready messages for conversation ${conversationId}`);

    } catch (error) {
      logger.error(`Error processing existing messages for conversation ${conversationId}`, error);
    }
  }

  /**
   * Register a callback to receive delivered messages for a conversation
   */
  static registerDeliveryCallback(conversationId: number, callback: (message: any) => void): void {
    if (!this.deliveryCallbacks.has(conversationId)) {
      this.deliveryCallbacks.set(conversationId, []);
    }
    this.deliveryCallbacks.get(conversationId)!.push(callback);
    logger.info(`📡 Registered delivery callback for conversation ${conversationId}`);
  }

  /**
   * Unregister a delivery callback
   */
  static unregisterDeliveryCallback(conversationId: number, callback: (message: any) => void): void {
    const callbacks = this.deliveryCallbacks.get(conversationId);
    if (callbacks) {
      const index = callbacks.indexOf(callback);
      if (index > -1) {
        callbacks.splice(index, 1);
        if (callbacks.length === 0) {
          this.deliveryCallbacks.delete(conversationId);
        }
        logger.info(`📡 Unregistered delivery callback for conversation ${conversationId}`);
      }
    }
  }

  /**
   * Process all ready messages for a conversation
   */
  private static async processConversationMessages(conversationId: number): Promise<void> {
    // Get messages that need validation
    const messagesNeedingValidation = await MessageQueueService.getMessagesNeedingValidation(conversationId);
    
    if (messagesNeedingValidation.length > 0) {
      logger.info(`🔍 Found ${messagesNeedingValidation.length} messages needing validation for conversation ${conversationId}`);
      
      // Auto-approve all messages for now (until we fix the validation system)
      for (const message of messagesNeedingValidation) {
        await this.autoApproveMessage(message);
      }
    }

    // Get validated messages ready to be sent
    const readyMessages = await MessageQueueService.getValidatedReadyMessages(conversationId);
    
    if (readyMessages.length > 0) {
      logger.info(`📤 Delivering ${readyMessages.length} validated messages for conversation ${conversationId}`);
      
      for (const message of readyMessages) {
        await this.deliverMessage(message);
      }
    }
  }

  /**
   * Auto-approve a message (temporary solution)
   */
  private static async autoApproveMessage(message: QueuedMessage): Promise<void> {
    logger.info(`✅ Auto-approving message ${message.id} from ${message.character}`);
    
    // Update validation status
    await MessageQueueService.updateValidationStatus(message.id, 'VALIDATED');
    
    // Update validation decision
    await this.db('forachat.message_queue')
      .where('id', message.id)
      .update({
        validation_decision: 'SEND_AS_IS',
        validation_reasoning: 'Auto-approved by MessageDeliveryService (validation system temporarily disabled)',
        validation_completed_at: new Date(),
        updated_at: new Date()
      });
  }

  /**
   * Deliver a message to the conversation and notify all clients
   */
  private static async deliverMessage(message: QueuedMessage): Promise<void> {
    logger.info(`📨 Delivering message ${message.id} from ${message.character}: ${message.text.substring(0, 50)}...`);
    
    // Add to conversation history
    await ConversationService.addMessage(
      message.character,
      message.text,
      message.conversation_id
    );

    // Mark as sent
    await MessageQueueService.updateMessageStatus(message.id, 'SENT');

    // Notify all registered callbacks
    const callbacks = this.deliveryCallbacks.get(message.conversation_id);
    if (callbacks && callbacks.length > 0) {
      const deliveryData = {
        id: message.id,
        character: message.character,
        text: message.text,
        timestamp: new Date(),
        conversationId: message.conversation_id
      };

      callbacks.forEach(callback => {
        try {
          callback(deliveryData);
        } catch (error) {
          logger.error(`Error in delivery callback for conversation ${message.conversation_id}:`, error);
        }
      });
      
      logger.info(`📡 Notified ${callbacks.length} clients about message delivery`);
    }
  }

  /**
   * Get active conversations being processed
   */
  static getActiveConversations(): number[] {
    return Array.from(this.processingIntervals.keys());
  }

  /**
   * Get statistics about the delivery service
   */
  static getStats(): {
    activeConversations: number;
    totalCallbacks: number;
    conversationCallbacks: Record<number, number>;
  } {
    const conversationCallbacks: Record<number, number> = {};
    let totalCallbacks = 0;

    for (const [conversationId, callbacks] of this.deliveryCallbacks.entries()) {
      conversationCallbacks[conversationId] = callbacks.length;
      totalCallbacks += callbacks.length;
    }

    return {
      activeConversations: this.processingIntervals.size,
      totalCallbacks,
      conversationCallbacks
    };
  }
}
