import { DBOS } from '@dbos-inc/dbos-sdk';
import { PromptService } from './PromptService';
import { ConversationService } from './ConversationService';
import { MoodService } from './MoodService';
import { MessageQueueService } from './MessageQueueService';
import { GeminiLLMService } from '../services/GeminiLLMService';
import { NotificationDispatcher } from '../streaming/NotificationDispatcher';
import { logger } from '../utils/Logger';
import {
  MessageValidationInput,
  MessageValidationResult,
  ValidationContext,
  QueuedMessage,
  Message,
  CharacterMoods,
  ValidationDecision
} from '../models/types';

export class MessageValidationService {
  private static notificationDispatcher = NotificationDispatcher.getInstance();

  /**
   * Validate a queued message by asking the character to review it
   * Now includes push notifications for validation status updates
   */
  @DBOS.workflow()
  static async validateQueuedMessage(messageId: number): Promise<MessageValidationResult> {
    // Get the queued message
    const queuedMessage = await MessageQueueService.getMessageById(messageId);
    if (!queuedMessage) {
      throw new Error(`Queued message not found: ${messageId}`);
    }

    // Mark message as validating
    await MessageQueueService.updateValidationStatus(messageId, 'VALIDATING');

    // Notify clients that validation has started
    await MessageValidationService.notifyValidationStarted(queuedMessage);

    try {
      // Build validation context
      const context = await MessageValidationService.buildValidationContext(queuedMessage);

      // Get character validation decision
      const validationResult = await MessageValidationService.getCharacterValidationDecision(
        queuedMessage,
        context
      );

      // Update message with validation result
      await MessageValidationService.applyValidationResult(messageId, validationResult);

      // Mark validation as completed
      await MessageQueueService.updateValidationStatus(messageId, 'VALIDATED');

      // Notify clients of validation result
      await MessageValidationService.notifyValidationCompleted(queuedMessage, validationResult);

      logger.info(`=== MESSAGE VALIDATION COMPLETED ===`);
      logger.info(`Message ID: ${messageId}, Character: ${queuedMessage.character}`);
      logger.info(`Decision: ${validationResult.decision}, Reasoning: ${validationResult.reasoning}`);

      return validationResult;

    } catch (error) {
      logger.error(`Error validating message ${messageId}`, error);

      // Reset validation status on error
      await MessageQueueService.updateValidationStatus(messageId, 'NOT_VALIDATED');

      // Notify clients of validation failure
      await MessageValidationService.notifyValidationFailed(queuedMessage, error as Error);

      // Return a default decision to send as-is on validation failure
      logger.error(`=== MESSAGE VALIDATION FAILED ===`);
      logger.error(`Message ID: ${messageId}, Character: ${queuedMessage.character}`);
      logger.error(`Error: ${error}`);

      return {
        decision: 'SEND_AS_IS',
        reasoning: 'Validation failed, sending original message'
      };
    }
  }

  /**
   * Build validation context including conversation history and character mood
   */
  @DBOS.transaction()
  static async buildValidationContext(queuedMessage: QueuedMessage): Promise<ValidationContext> {
    // Get conversation history
    const conversationHistory = await ConversationService.getConversationMessages(queuedMessage.conversation_id);
    
    // Get character moods
    const conversation = await ConversationService.getConversation(queuedMessage.conversation_id);
    const characterMoods = conversation?.character_moods 
      ? MoodService.parseCharacterMoods(conversation.character_moods)
      : null;

    return {
      originalMessage: queuedMessage,
      conversationHistory,
      characterMoods
    };
  }

  /**
   * Get character's validation decision using LLM
   */
  @DBOS.step()
  static async getCharacterValidationDecision(
    queuedMessage: QueuedMessage,
    context: ValidationContext
  ): Promise<MessageValidationResult> {
    // Get character-specific validation system prompt
    const characterPromptName = `${queuedMessage.character.toLowerCase()}_validation_system`;
    let systemPrompt = await PromptService.getSystemPrompt(characterPromptName);

    // Add mood context if available
    if (context.characterMoods) {
      const moodContext = MoodService.formatCharacterMoodForPrompt(queuedMessage.character, context.characterMoods);
      if (moodContext) {
        systemPrompt = `${systemPrompt}\n\n${moodContext}`;
      }
    }

    // Build validation prompt
    const validationPrompt = MessageValidationService.buildValidationPrompt(queuedMessage, context);

    // Get LLM decision
    const llmResponse = await GeminiLLMService.generateJSONStatic(systemPrompt, validationPrompt);

    // Validate and parse response
    return MessageValidationService.parseValidationResponse(llmResponse);
  }

  /**
   * Build the validation prompt for the character
   */
  private static buildValidationPrompt(queuedMessage: QueuedMessage, context: ValidationContext): string {
    // Format conversation history
    const historyText = context.conversationHistory
      .slice(-10) // Last 10 messages for context
      .map(msg => `${msg.character}: ${msg.text}`)
      .join('\n');

    // Calculate time since message was queued
    const queuedTime = new Date(queuedMessage.created_at);
    const now = new Date();
    const minutesAgo = Math.floor((now.getTime() - queuedTime.getTime()) / (1000 * 60));

    return `You queued this message ${minutesAgo} minutes ago:
"${queuedMessage.text}"

Here's what has happened in the conversation since then:
${historyText}

Review your queued message in light of the current conversation. You don't need to reply to everything - it's okay to withdraw messages that no longer add value.

Consider if your message is still relevant, timely, and adds meaningful contribution.

Choose one of these options:
- SEND_AS_IS: Send the message exactly as originally written
- WITHDRAW: Don't send the message (remove from queue)  
- REVISE: Modify the message content before sending

Respond with JSON in this format:
{
  "decision": "SEND_AS_IS" | "WITHDRAW" | "REVISE",
  "reasoning": "Brief explanation of your decision",
  "revisedText": "New message text (only if decision is REVISE)"
}`;
  }

  /**
   * Parse and validate the LLM response
   */
  private static parseValidationResponse(llmResponse: any): MessageValidationResult {
    if (!llmResponse || typeof llmResponse !== 'object') {
      throw new Error('Invalid LLM response format');
    }

    const { decision, reasoning, revisedText } = llmResponse;

    // Validate decision
    const validDecisions: ValidationDecision[] = ['SEND_AS_IS', 'WITHDRAW', 'REVISE'];
    if (!validDecisions.includes(decision)) {
      throw new Error(`Invalid validation decision: ${decision}`);
    }

    // Validate required fields
    if (!reasoning || typeof reasoning !== 'string') {
      throw new Error('Missing or invalid reasoning');
    }

    // Validate revised text if decision is REVISE
    if (decision === 'REVISE' && (!revisedText || typeof revisedText !== 'string')) {
      throw new Error('Missing revised text for REVISE decision');
    }

    return {
      decision,
      reasoning,
      revisedText: decision === 'REVISE' ? revisedText : undefined
    };
  }

  /**
   * Apply validation result to the queued message
   */
  @DBOS.transaction()
  static async applyValidationResult(messageId: number, result: MessageValidationResult): Promise<void> {
    const { decision, reasoning, revisedText } = result;

    // Update validation fields
    await DBOS.knexClient('forachat.message_queue')
      .where('id', messageId)
      .update({
        validation_decision: decision,
        validation_reasoning: reasoning,
        validation_completed_at: new Date(),
        updated_at: new Date()
      });

    // Handle specific decisions
    switch (decision) {
      case 'WITHDRAW':
        await MessageQueueService.updateMessageStatus(messageId, 'WITHDRAWN');
        break;
        
      case 'REVISE':
        if (revisedText) {
          // Store original text if not already stored
          const message = await MessageQueueService.getMessageById(messageId);
          if (message && !message.original_text) {
            await DBOS.knexClient('forachat.message_queue')
              .where('id', messageId)
              .update({ original_text: message.text });
          }
          
          // Update message text
          await MessageQueueService.updateMessageText(messageId, revisedText);
        }
        break;
        
      case 'SEND_AS_IS':
        // No additional action needed
        break;
    }
  }

  /**
   * Get validation statistics for monitoring
   */
  @DBOS.transaction()
  static async getValidationStats(conversationId?: number): Promise<{
    total: number;
    validated: number;
    pending: number;
    sendAsIs: number;
    withdrawn: number;
    revised: number;
    failed: number;
  }> {
    let query = DBOS.knexClient('forachat.message_queue');

    if (conversationId) {
      query = query.where('conversation_id', conversationId);
    }

    const stats = await query
      .select([
        DBOS.knexClient.raw('COUNT(*) as total'),
        DBOS.knexClient.raw('SUM(CASE WHEN validation_status = \'VALIDATED\' THEN 1 ELSE 0 END) as validated'),
        DBOS.knexClient.raw('SUM(CASE WHEN validation_status = \'NOT_VALIDATED\' THEN 1 ELSE 0 END) as pending'),
        DBOS.knexClient.raw('SUM(CASE WHEN validation_decision = \'SEND_AS_IS\' THEN 1 ELSE 0 END) as sendAsIs'),
        DBOS.knexClient.raw('SUM(CASE WHEN validation_decision = \'WITHDRAW\' THEN 1 ELSE 0 END) as withdrawn'),
        DBOS.knexClient.raw('SUM(CASE WHEN validation_decision = \'REVISE\' THEN 1 ELSE 0 END) as revised'),
        DBOS.knexClient.raw('SUM(CASE WHEN validation_status = \'VALIDATING\' AND validation_attempts > 1 THEN 1 ELSE 0 END) as failed')
      ])
      .first();

    return {
      total: parseInt(stats.total) || 0,
      validated: parseInt(stats.validated) || 0,
      pending: parseInt(stats.pending) || 0,
      sendAsIs: parseInt(stats.sendAsIs) || 0,
      withdrawn: parseInt(stats.withdrawn) || 0,
      revised: parseInt(stats.revised) || 0,
      failed: parseInt(stats.failed) || 0
    };
  }

  // ===== PUSH NOTIFICATION METHODS =====

  /**
   * Notify clients that message validation has started
   */
  private static async notifyValidationStarted(queuedMessage: QueuedMessage): Promise<void> {
    const notification = {
      id: MessageValidationService.notificationDispatcher.generateMessageId(),
      type: 'validation_request' as const,
      data: {
        messageId: queuedMessage.id,
        character: queuedMessage.character,
        text: queuedMessage.text,
        status: 'validating'
      },
      timestamp: new Date(),
      priority: 3,
      conversationId: queuedMessage.conversation_id
    };

    await MessageValidationService.notificationDispatcher.notifyConversation(
      queuedMessage.conversation_id,
      notification
    );

    logger.info(`📡 Notified clients: validation started for message ${queuedMessage.id}`);
  }

  /**
   * Notify clients of validation completion
   */
  private static async notifyValidationCompleted(
    queuedMessage: QueuedMessage,
    result: MessageValidationResult
  ): Promise<void> {
    const notification = {
      id: MessageValidationService.notificationDispatcher.generateMessageId(),
      type: 'validation_result' as const,
      data: {
        messageId: queuedMessage.id,
        character: queuedMessage.character,
        originalText: queuedMessage.text,
        decision: result.decision,
        reasoning: result.reasoning,
        revisedText: result.revisedText,
        status: 'completed'
      },
      timestamp: new Date(),
      priority: 2,
      conversationId: queuedMessage.conversation_id
    };

    await MessageValidationService.notificationDispatcher.notifyConversation(
      queuedMessage.conversation_id,
      notification
    );

    logger.info(`📡 Notified clients: validation completed for message ${queuedMessage.id} - ${result.decision}`);
  }

  /**
   * Notify clients of validation failure
   */
  private static async notifyValidationFailed(queuedMessage: QueuedMessage, error: Error): Promise<void> {
    const notification = {
      id: MessageValidationService.notificationDispatcher.generateMessageId(),
      type: 'validation_result' as const,
      data: {
        messageId: queuedMessage.id,
        character: queuedMessage.character,
        text: queuedMessage.text,
        status: 'failed',
        error: error.message
      },
      timestamp: new Date(),
      priority: 1,
      conversationId: queuedMessage.conversation_id
    };

    await MessageValidationService.notificationDispatcher.notifyConversation(
      queuedMessage.conversation_id,
      notification
    );

    logger.info(`📡 Notified clients: validation failed for message ${queuedMessage.id}`);
  }

  /**
   * Batch validate multiple messages and notify progress
   */
  @DBOS.workflow()
  static async batchValidateMessages(messageIds: number[]): Promise<MessageValidationResult[]> {
    const results: MessageValidationResult[] = [];

    for (const messageId of messageIds) {
      try {
        const result = await MessageValidationService.validateQueuedMessage(messageId);
        results.push(result);
      } catch (error) {
        logger.error(`Error in batch validation for message ${messageId}`, error);
        results.push({
          decision: 'SEND_AS_IS',
          reasoning: `Batch validation failed: ${(error as Error).message}`
        });
      }
    }

    // Notify clients of batch completion
    const notification = {
      id: MessageValidationService.notificationDispatcher.generateMessageId(),
      type: 'system_notification' as const,
      data: {
        event: 'batch_validation_complete',
        messageCount: messageIds.length,
        results: results.map(r => r.decision)
      },
      timestamp: new Date(),
      priority: 3
    };

    await MessageValidationService.notificationDispatcher.broadcast(notification);

    return results;
  }
}
