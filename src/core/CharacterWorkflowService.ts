import { DBOS, WorkflowQueue } from '@dbos-inc/dbos-sdk';
import { ConversationService } from './ConversationService';
import { PromptService } from './PromptService';
import { MessageQueueService } from './MessageQueueService';
import { ConversationDecayService } from './ConversationDecayService';
import { LLMService } from '../services/LLMService';
import { logger } from '../utils/Logger';
import { GeminiLLMService } from '../services/GeminiLLMService';
import { MoodService } from './MoodService';

// Character queue for background thoughts - initialized at module load time
const characterQueue = new WorkflowQueue("character_thoughts", { concurrency: 10 });

// Track active character workflows to prevent duplicates
const activeCharacterWorkflows = new Map<string, Set<string>>();

export class CharacterWorkflowService {
    // Static LLM service instance for dependency injection
    private static llmService: LLMService | null = null;

    // Method to set LLM service for testing
    static setLLMService(service: LLMService): void {
        CharacterWorkflowService.llmService = service;
    }

    // Helper method to get the LLM service (injected or default)
    private static getLLMService(): LLMService {
        return CharacterWorkflowService.llmService || new GeminiLLMService();
    }

    static getCharacterQueue(): WorkflowQueue {
        return characterQueue;
    }

    static isCharacterWorkflowActive(conversationId: number, character: string): boolean {
        const conversationWorkflows = activeCharacterWorkflows.get(conversationId.toString());
        return conversationWorkflows?.has(character) || false;
    }

    static markCharacterWorkflowActive(conversationId: number, character: string): void {
        const conversationKey = conversationId.toString();
        if (!activeCharacterWorkflows.has(conversationKey)) {
            activeCharacterWorkflows.set(conversationKey, new Set());
        }
        activeCharacterWorkflows.get(conversationKey)!.add(character);
    }

    static markCharacterWorkflowComplete(conversationId: number, character: string): void {
        const conversationKey = conversationId.toString();
        const conversationWorkflows = activeCharacterWorkflows.get(conversationKey);
        if (conversationWorkflows) {
            conversationWorkflows.delete(character);
            if (conversationWorkflows.size === 0) {
                activeCharacterWorkflows.delete(conversationKey);
            }
        }
    }

    @DBOS.workflow()
    static async delayedCharacterThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string,
        initialDelay: number
    ): Promise<any> {
        logger.info(`🚀 Starting delayedCharacterThoughtWorkflow for ${character} with delay ${initialDelay}ms, conversation ${conversationId}`);

        // Apply initial delay using DBOS.sleep for proper workflow handling
        logger.info(`⏰ Sleeping for ${initialDelay}ms before generating character thought for ${character}`);
        await DBOS.sleep(initialDelay);

        logger.info(`⏰ Sleep completed for ${character}, now generating character thought`);

        try {
            // Check conversation engagement and apply decay
            const decayInfo = await ConversationDecayService.updateConversationEngagement(conversationId);

            if (decayInfo.shouldTimeout) {
                logger.info(`Conversation ${conversationId} timed out due to low engagement (${Math.round(decayInfo.engagementLevel * 100)}%). Skipping ${character} thought.`);
                CharacterWorkflowService.markCharacterWorkflowComplete(conversationId, character);
                return null;
            }

            const adjustedDelay = ConversationDecayService.applyDecayToDelay(initialDelay, decayInfo.delayMultiplier);

            if (adjustedDelay === Infinity) {
                logger.info(`Conversation ${conversationId} delay timeout. Skipping ${character} thought.`);
                CharacterWorkflowService.markCharacterWorkflowComplete(conversationId, character);
                return null;
            }

            const lastActivity = await ConversationService.getLastUserActivity(conversationId);
            const timeSinceActivity = lastActivity ? Date.now() - lastActivity.getTime() : 0;
            const decayStatus = ConversationDecayService.getDecayStatus(
                timeSinceActivity,
                decayInfo.engagementLevel,
                decayInfo.delayMultiplier
            );

            logger.info(`${character} thought scheduled with decay: ${decayStatus}`);

            // Apply additional delay if needed
            if (adjustedDelay > initialDelay) {
                await DBOS.sleep(adjustedDelay - initialDelay);
            }

            // Now execute the actual character thought workflow
            return await CharacterWorkflowService.characterThoughtWorkflow(conversationId, context, character);
        } catch (error) {
            logger.error(`Error in delayed character thought workflow for ${character}`, error);
            CharacterWorkflowService.markCharacterWorkflowComplete(conversationId, character);
            return null;
        }
    }

    @DBOS.workflow()
    static async characterThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string
    ): Promise<any> {
        // Check if this character already has an active workflow to prevent duplicates
        if (CharacterWorkflowService.isCharacterWorkflowActive(conversationId, character)) {
            logger.info(`Skipping ${character} thought - workflow already active for this character`);
            return null;
        }

        // Mark this workflow as active
        CharacterWorkflowService.markCharacterWorkflowActive(conversationId, character);

        try {
            // Check for recent messages from this character to prevent immediate self-replies
            const recentMessages = await ConversationService.getDelayedThoughts(conversationId);
            if (recentMessages.length > 0) {
                // Check for recent messages from this character (within last 5 minutes)
                const fiveMinutesAgo = Date.now() - 300000;
                const recentCharacterMessages = recentMessages
                    .filter(msg => msg.character === character)
                    .filter(msg => new Date(msg.created_at!).getTime() > fiveMinutesAgo);

                if (recentCharacterMessages.length > 0) {
                    const lastMessage = recentCharacterMessages[recentCharacterMessages.length - 1];
                    const timeSinceLastMessage = Date.now() - new Date(lastMessage.created_at!).getTime();
                    const MIN_COOLDOWN = 30000; // 30 seconds minimum between same character messages

                    if (timeSinceLastMessage < MIN_COOLDOWN) {
                        logger.info(`Skipping ${character} thought - too soon after their last message (${timeSinceLastMessage}ms < ${MIN_COOLDOWN}ms)`);
                        CharacterWorkflowService.markCharacterWorkflowComplete(conversationId, character);
                        return null;
                    }
                }
            }

            // Get pending messages to provide context for better responses
            const pendingMessages = await MessageQueueService.getPendingMessages(conversationId);
            const pendingContext = pendingMessages.length > 0
                ? `\n\nPending messages that will appear soon:\n${pendingMessages.map(msg => `${msg.character}: ${msg.text}`).join('\n')}`
                : '';

            // Add a delay before generating the thought (simulate thinking time)
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 8000));

            // Use character-specific system prompt with mood context
            const characterPromptName = `${character.toLowerCase()}_system`;
            let systemPrompt = await PromptService.getSystemPrompt(characterPromptName);

            // Add mood context to the system prompt
            const conversation = await ConversationService.getConversation(conversationId);
            if (conversation?.character_moods) {
                const moods = MoodService.parseCharacterMoods(conversation.character_moods);
                if (moods) {
                    const moodContext = MoodService.formatCharacterMoodForPrompt(character, moods);
                    if (moodContext) {
                        systemPrompt = `${systemPrompt}\n\n${moodContext}`;
                    }
                }
            }

            const thoughtPrompt = `Based on this conversation context, generate a brief follow-up thought or comment that adds value to the discussion.

Consider any pending messages that will appear soon to avoid redundancy or similar content.

Context:
${context}${pendingContext}

Respond with a natural, contextual message that continues the conversation. You must respond with JSON in exactly this format:

{
  "reply": [
    {
      "character": "${character}",
      "text": "your_message_here",
      "delay": 2000
    }
  ],
  "skills": [],
  "theme": "follow-up thought"
}`;

            const llmService = CharacterWorkflowService.getLLMService();
            const llmResponse = await llmService.generate(systemPrompt, thoughtPrompt);

            // Log parsed LLM response with character context
            logger.info(`=== LLM RESPONSE (CHARACTER THOUGHT) ===`);
            logger.info(`Conversation: ${conversationId}, Responder: ${character} character`);
            logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

            if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
                const thought = llmResponse.reply[0];

                // Try to enqueue the message (this will handle duplicate/similarity checking)
                const queuedMessage = await MessageQueueService.enqueueMessage({
                    conversation_id: conversationId,
                    character: thought.character,
                    text: thought.text,
                    delay_ms: thought.delay || 2000
                });

                if (queuedMessage) {
                    logger.info(`Enqueued ${character} thought: ${thought.text.substring(0, 50)}...`);
                    return {
                        character,
                        text: thought.text,
                        conversationId
                    };
                } else {
                    logger.info(`${character} thought was filtered out (duplicate/similar)`);
                    return null;
                }
            }

            return null;
        } finally {
            // Always mark the workflow as complete
            CharacterWorkflowService.markCharacterWorkflowComplete(conversationId, character);
        }
    }

    @DBOS.workflow()
    static async extendedWorkflowThoughtWorkflow(
        conversationId: number,
        context: string,
        character: string
    ): Promise<any> {
        // Check if this character already has an active workflow to prevent duplicates
        if (CharacterWorkflowService.isCharacterWorkflowActive(conversationId, character)) {
            logger.info(`Skipping ${character} extended thought - workflow already active for this character`);
            return null;
        }

        // Mark this workflow as active
        CharacterWorkflowService.markCharacterWorkflowActive(conversationId, character);

        try {
            // Check for recent messages from this character to prevent immediate self-replies
            const recentMessages = await ConversationService.getDelayedThoughts(conversationId);
            if (recentMessages.length > 0) {
                // Check for recent messages from this character (within last 5 minutes)
                const fiveMinutesAgo = Date.now() - 300000;
                const recentCharacterMessages = recentMessages
                    .filter(msg => msg.character === character)
                    .filter(msg => new Date(msg.created_at!).getTime() > fiveMinutesAgo);

                if (recentCharacterMessages.length > 0) {
                    const lastMessage = recentCharacterMessages[recentCharacterMessages.length - 1];
                    const timeSinceLastMessage = Date.now() - new Date(lastMessage.created_at!).getTime();
                    const MIN_COOLDOWN = 30000; // 30 seconds minimum between same character messages

                    if (timeSinceLastMessage < MIN_COOLDOWN) {
                        logger.info(`Skipping ${character} extended thought - too soon after their last message (${timeSinceLastMessage}ms < ${MIN_COOLDOWN}ms)`);
                        CharacterWorkflowService.markCharacterWorkflowComplete(conversationId, character);
                        return null;
                    }
                }
            }

            // Add a delay before generating the thought (simulate thinking time)
            await new Promise(resolve => setTimeout(resolve, 2000 + Math.random() * 8000));

            // Use character-specific system prompt with mood context
            const characterPromptName = `${character.toLowerCase()}_system`;
            let systemPrompt = await PromptService.getSystemPrompt(characterPromptName);

            // Add mood context to the system prompt
            const conversation = await ConversationService.getConversation(conversationId);
            if (conversation?.character_moods) {
                const moods = MoodService.parseCharacterMoods(conversation.character_moods);
                if (moods) {
                    const moodContext = MoodService.formatCharacterMoodForPrompt(character, moods);
                    if (moodContext) {
                        systemPrompt = `${systemPrompt}\n\n${moodContext}`;
                    }
                }
            }

            const thoughtPrompt = `Based on this conversation context, generate a brief follow-up thought or comment that adds value to the discussion.

This is for an extended workflow message that will be sent immediately, not queued.

Context:
${context}

Respond with a natural, contextual message that continues the conversation. You must respond with JSON in exactly this format:

{
  "reply": [
    {
      "character": "${character}",
      "text": "your_message_here",
      "delay": 2000
    }
  ],
  "skills": [],
  "theme": "extended workflow thought"
}`;

            const llmService = CharacterWorkflowService.getLLMService();
            const llmResponse = await llmService.generate(systemPrompt, thoughtPrompt);

            // Log parsed LLM response with character context
            logger.info(`=== LLM RESPONSE (EXTENDED WORKFLOW THOUGHT) ===`);
            logger.info(`Conversation: ${conversationId}, Responder: ${character} character`);
            logger.info(`Parsed Response: ${JSON.stringify(llmResponse)}`);

            if (llmResponse && llmResponse.reply && llmResponse.reply.length > 0) {
                const thought = llmResponse.reply[0];

                // For extended workflow, we DON'T enqueue the message - it will be sent immediately
                // This prevents duplication with delayed_thought messages
                logger.info(`Generated ${character} extended workflow thought: ${thought.text.substring(0, 50)}...`);
                return {
                    character,
                    text: thought.text,
                    conversationId
                };
            }

            return null;
        } finally {
            // Always mark the workflow as complete
            CharacterWorkflowService.markCharacterWorkflowComplete(conversationId, character);
        }
    }

    // Simple test method without queue to verify workflow execution
    @DBOS.workflow()
    static async simpleTestWorkflow(message: string): Promise<string> {
        logger.info(`🧪 Simple test workflow executed with message: ${message}`);
        await DBOS.sleep(1000); // 1 second delay
        logger.info('🧪 Simple test workflow completed after 1 second delay');
        return `Simple test completed: ${message}`;
    }
}
