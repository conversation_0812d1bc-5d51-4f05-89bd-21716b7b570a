# Enhanced REPL UI with Fixed Input Line

## Overview

The ForaChat REPL has been enhanced with a new terminal user interface that provides:

- **Fixed input line at the bottom**: User input stays at the bottom of the screen
- **Scrollable chat area**: Messages scroll above the input line
- **Color-coded messages**: Different characters have different colors for better readability
- **Real-time updates**: Messages appear in the chat area as they arrive
- **Clean separation**: Clear visual separation between chat history and input

## Features

### Visual Layout
```
┌─────────────────────────────────────────────────┐
│                                                 │
│  Chat Messages (Scrollable)                     │
│  [timestamp] character: message                  │
│  [timestamp] character: message                  │
│  ...                                            │
│                                                 │
├─────────────────────────────────────────────────┤
│ Input (Press Enter to send, Ctrl+C to exit)     │
│ > your message here_                            │
└─────────────────────────────────────────────────┘
```

### Color Coding
- **Fora**: Green
- **Jan**: Blue  
- **Lou**: Magenta
- **User**: White
- **System**: Yellow
- **Other**: Cyan

### Key Features
1. **Auto-scroll**: Chat automatically scrolls to show new messages
2. **Timestamps**: All messages include timestamps
3. **Staggered display**: Messages appear with their configured delays
4. **Session management**: Visual feedback for session creation/restoration
5. **Error handling**: Errors displayed in red in the chat area

## Usage

The enhanced REPL works exactly like the previous version:

```bash
# Start with local server
npm run repl -- --local

# Connect to existing server
npm run repl

# Resume a session
npm run repl -- --session-id your-session-id
```

## Controls

- **Enter**: Send message
- **Ctrl+C**: Exit the REPL
- **Type "exit" or "quit"**: Alternative way to exit

## Technical Implementation

The enhancement uses the `blessed` library to create a terminal UI with:
- Screen management for full terminal control
- Scrollable text boxes for chat display
- Input boxes for user interaction
- Event handling for keyboard input
- Color support for message formatting

## Server Quiet Mode

When running with the `--local` flag, the server starts in quiet mode to prevent server logging from interfering with the blessed UI:

- **Persistent Quiet Mode**: Server logging is suppressed for the entire REPL session
- **Log File Redirection**: Server logs are redirected to `server.log` instead of console
- **Clean Startup**: Screen is cleared before the blessed UI takes control
- **No Interference**: Server messages (including WebSocket logs) don't overwrite the input line
- **Background Operation**: Server runs silently in the background
- **Session Logs**: All DBOS session management and WebSocket connection logs are captured

## Benefits

1. **Better UX**: Input line stays visible and accessible
2. **Improved readability**: Color coding and clear layout
3. **Professional appearance**: Clean, modern terminal interface
4. **Consistent behavior**: Input always available at bottom
5. **Better message flow**: Clear separation between input and output
6. **Clean Local Mode**: Server logging doesn't interfere with the UI when using --local
