const { MessageValidationService } = require('./dist/core/MessageValidationService');

async function testValidation() {
  console.log('=== TESTING MESSAGE VALIDATION ===\n');
  
  try {
    // Test validation on message ID 235 (<PERSON>'s message)
    const messageId = 235;
    console.log(`Testing validation for message ID: ${messageId}`);
    
    const result = await MessageValidationService.validateQueuedMessage(messageId);
    
    console.log('Validation completed successfully!');
    console.log('Result:', result);
    
  } catch (error) {
    console.error('Validation failed:', error);
  }
}

testValidation();
