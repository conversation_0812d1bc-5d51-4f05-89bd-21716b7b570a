const knex = require('knex');
const config = require('./knexfile.cjs');

async function checkValidationStatus() {
  const db = knex(config.development);
  
  try {
    console.log('=== MESSAGE QUEUE VALIDATION STATUS ===\n');
    
    // Get recent messages
    const recentMessages = await db('forachat.message_queue')
      .select('id', 'character', 'status', 'validation_status', 'validation_decision', 'scheduled_at', 'created_at')
      .orderBy('created_at', 'desc')
      .limit(10);
    
    console.log('Recent messages:');
    recentMessages.forEach(msg => {
      const scheduledTime = new Date(msg.scheduled_at);
      const now = new Date();
      const isReady = scheduledTime <= now;
      
      console.log(`ID: ${msg.id}, Character: ${msg.character}, Status: ${msg.status}, Validation: ${msg.validation_status}, Decision: ${msg.validation_decision || 'none'}, Ready: ${isReady ? 'YES' : 'NO'}`);
    });
    
    // Get validation stats
    const stats = await db('forachat.message_queue')
      .select(
        db.raw('COUNT(*) as total'),
        db.raw('SUM(CASE WHEN validation_status = \'NOT_VALIDATED\' THEN 1 ELSE 0 END) as not_validated'),
        db.raw('SUM(CASE WHEN validation_status = \'VALIDATING\' THEN 1 ELSE 0 END) as validating'),
        db.raw('SUM(CASE WHEN validation_status = \'VALIDATED\' THEN 1 ELSE 0 END) as validated'),
        db.raw('SUM(CASE WHEN status = \'PENDING\' THEN 1 ELSE 0 END) as pending'),
        db.raw('SUM(CASE WHEN status = \'SENT\' THEN 1 ELSE 0 END) as sent')
      )
      .first();
    
    console.log('\n=== VALIDATION STATISTICS ===');
    console.log(`Total messages: ${stats.total}`);
    console.log(`Not validated: ${stats.not_validated}`);
    console.log(`Currently validating: ${stats.validating}`);
    console.log(`Validated: ${stats.validated}`);
    console.log(`Pending: ${stats.pending}`);
    console.log(`Sent: ${stats.sent}`);
    
    // Check for messages that should be getting validated
    const now = new Date();
    const needingValidation = await db('forachat.message_queue')
      .where('status', 'PENDING')
      .where('scheduled_at', '<=', now)
      .where('validation_status', 'NOT_VALIDATED')
      .select('id', 'character', 'conversation_id', 'scheduled_at', 'created_at');
    
    console.log('\n=== MESSAGES NEEDING VALIDATION ===');
    if (needingValidation.length > 0) {
      console.log(`Found ${needingValidation.length} messages that should be getting validated:`);
      // Show only the most recent 10 to avoid spam
      needingValidation.slice(-10).forEach(msg => {
        console.log(`ID: ${msg.id}, Character: ${msg.character}, Conversation: ${msg.conversation_id}, Scheduled: ${msg.scheduled_at}`);
      });
      if (needingValidation.length > 10) {
        console.log(`... and ${needingValidation.length - 10} more`);
      }
    } else {
      console.log('No messages currently need validation');
    }

    // Check active sessions
    const activeSessions = await db('forachat.sessions')
      .where('expires_at', '>', new Date())
      .select('id', 'conversation_id', 'channel', 'last_activity');

    console.log('\n=== ACTIVE SESSIONS ===');
    if (activeSessions.length > 0) {
      console.log(`Found ${activeSessions.length} active sessions:`);
      activeSessions.forEach(session => {
        console.log(`Session: ${session.id.substring(0, 8)}..., Conversation: ${session.conversation_id}, Channel: ${session.channel}, Last Activity: ${session.last_activity}`);
      });
    } else {
      console.log('No active sessions found');
    }
    
  } catch (error) {
    console.error('Error checking validation status:', error);
  } finally {
    await db.destroy();
  }
}

checkValidationStatus();
