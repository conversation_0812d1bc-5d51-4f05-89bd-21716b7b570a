# Push-Based Messaging System Architecture

## Overview
This document outlines the comprehensive push-based messaging system that will replace the current polling architecture across all communication channels in ForaChat.

## Current Architecture Issues
- **Polling Overhead**: MessageQueueProcessor and MessageDeliveryService use setInterval polling every 1 second
- **Latency**: Messages have artificial delays due to polling intervals
- **Resource Usage**: Continuous polling consumes unnecessary CPU and database resources
- **Scalability**: Polling doesn't scale well with multiple conversations and clients

## New Push-Based Architecture

### 1. Unified Client Notification System

**NotificationDispatcher Class**
```typescript
interface NotificationTarget {
  type: 'websocket' | 'sse' | 'repl' | 'sms' | 'slack' | 'teams';
  sessionId: string;
  conversationId?: number;
  metadata?: any;
}

interface NotificationMessage {
  id: string;
  type: 'message' | 'typing' | 'validation' | 'system';
  data: any;
  timestamp: Date;
  priority: number;
}

class NotificationDispatcher {
  // Central registry of all active clients
  private clients: Map<string, NotificationTarget[]>;
  
  // Register client for notifications
  registerClient(sessionId: string, target: NotificationTarget): void;
  
  // Remove client from notifications
  unregisterClient(sessionId: string, targetType?: string): void;
  
  // Send notification to specific session
  notifySession(sessionId: string, message: NotificationMessage): Promise<void>;
  
  // Send notification to all clients in a conversation
  notifyConversation(conversationId: number, message: NotificationMessage): Promise<void>;
  
  // Broadcast to all connected clients
  broadcast(message: NotificationMessage): Promise<void>;
}
```

### 2. Server-Sent Events (SSE) Implementation

**SSE Endpoint Structure**
- `GET /events/:sessionId` - SSE stream for specific session
- `GET /events/conversation/:conversationId` - SSE stream for conversation updates
- Connection management with automatic reconnection support
- Proper error handling and connection cleanup

**SSE Message Format**
```typescript
interface SSEMessage {
  id: string;
  event: string;
  data: string; // JSON stringified NotificationMessage
  retry?: number;
}
```

### 3. Enhanced WebSocket Push Notifications

**Message Types**
- `message` - Character messages with timing
- `typing_start/typing_stop` - Typing indicators
- `validation_request` - Message validation prompts
- `validation_result` - Validation decisions
- `queue_update` - Queue status changes
- `conversation_update` - Theme/skills changes
- `system_notification` - System events

### 4. Server-Side Message Validation System

**Validation Flow**
1. Message queued → Validation scheduled
2. Character prompted with context → Decision made
3. Result pushed to all clients → Message processed accordingly

**Validation States**
- `PENDING` - Awaiting validation
- `VALIDATING` - Currently being validated
- `APPROVED` - Ready for delivery
- `REVISED` - Text updated, re-queue
- `WITHDRAWN` - Removed from queue

### 5. Event-Driven Message Processing

**Replace Polling with Events**
- Database triggers for queue changes
- Event emitters for message state transitions
- Reactive processing based on queue events
- Immediate notification dispatch

**Event Types**
```typescript
interface MessageEvent {
  type: 'queued' | 'validated' | 'ready' | 'sent' | 'failed';
  messageId: number;
  conversationId: number;
  timestamp: Date;
  data?: any;
}
```

## Implementation Strategy

### Phase 1: Core Infrastructure
1. Create NotificationDispatcher class
2. Implement SSE endpoints and connection management
3. Enhance WebSocket message types and handling

### Phase 2: Message Validation
1. Implement server-side validation system
2. Create validation workflow with character decision-making
3. Integrate validation results with push notifications

### Phase 3: Remove Polling
1. Replace MessageQueueProcessor polling with event-driven processing
2. Update MessageDeliveryService to use push notifications
3. Remove all setInterval-based polling mechanisms

### Phase 4: Client Integration
1. Update REPL interface for push-based messaging
2. Integrate SMS, Slack, Teams with unified dispatcher
3. Remove any remaining client-side polling dependencies

### Phase 5: Testing and Optimization
1. Comprehensive testing across all client types
2. Performance optimization and monitoring
3. Error handling and recovery mechanisms

## Benefits of Push-Based Architecture

1. **Real-time Responsiveness**: Immediate message delivery without polling delays
2. **Resource Efficiency**: Eliminates continuous polling overhead
3. **Better Scalability**: Event-driven architecture scales better with load
4. **Consistent Experience**: Unified notification system across all clients
5. **Enhanced Features**: Proper message validation with real-time feedback

## Technical Considerations

- **Connection Management**: Proper cleanup of SSE and WebSocket connections
- **Error Handling**: Graceful degradation when push mechanisms fail
- **Message Ordering**: Maintain proper message sequence across push channels
- **Duplicate Prevention**: Ensure messages aren't delivered multiple times
- **Session Persistence**: Handle client reconnections and session restoration
