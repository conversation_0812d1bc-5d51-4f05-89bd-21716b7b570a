# ForaChat Integration Testing

This document describes the integration testing system for ForaChat, which allows you to run automated tests against the live database and LLM services.

## Overview

The integration test runner provides a comprehensive way to test ForaChat's complete functionality including:

- **Server startup and initialization**
- **Real-time chat interactions**
- **Delayed character responses**
- **Conversation flow and continuity**
- **LLM integration and response quality**
- **Database operations and persistence**
- **WebSocket and HTTP API endpoints**

## Quick Start

### 1. Run a Quick Test
```bash
# Test with 2 simple prompts
npm run test:integration-live -- --prompts-file test-prompts/quick-test.txt
```

### 2. Run Basic Test Suite
```bash
# Test with 15 common workplace scenarios
npm run test:integration-live -- --prompts-file test-prompts/basic-prompts.txt
```

### 3. Run Structured Tests
```bash
# Test with validation of themes and skills
npm run test:integration-live -- --prompts-file test-prompts/structured-prompts.json --log-level verbose
```

### 4. Use Helper <PERSON>t
```bash
# Quick predefined scenarios
./scripts/run-tests.sh quick
./scripts/run-tests.sh basic
./scripts/run-tests.sh structured
./scripts/run-tests.sh verbose
```

## Architecture

### Components

1. **IntegrationTestRunner** (`src/scripts/integration-test-runner.ts`)
   - Core test execution engine
   - Server management and health checking
   - HTTP client for API interactions
   - Delayed message polling system
   - Result collection and reporting

2. **CLI Interface** (`src/scripts/run-integration-tests.ts`)
   - Command-line argument parsing
   - File loading and validation
   - Process management and cleanup
   - Error handling and reporting

3. **Test Prompts** (`test-prompts/`)
   - Sample test cases in multiple formats
   - Basic and structured prompt collections
   - Documentation and usage examples

4. **Helper Scripts** (`scripts/`)
   - Convenience wrappers for common scenarios
   - Predefined test configurations
   - Simplified command-line interface

### Test Flow

```mermaid
graph TD
    A[Load Prompts] --> B[Start Server]
    B --> C[Health Check]
    C --> D[Send Prompt]
    D --> E[Receive Initial Response]
    E --> F[Poll for Delayed Messages]
    F --> G[Collect Results]
    G --> H{More Prompts?}
    H -->|Yes| D
    H -->|No| I[Generate Report]
    I --> J[Save Results]
    J --> K[Cleanup]
```

## Test Types

### 1. Smoke Tests
**Purpose**: Verify basic functionality
**File**: `test-prompts/quick-test.txt`
**Duration**: ~2-3 minutes
**Use Case**: Quick validation after code changes

```bash
npm run test:integration-live -- -f test-prompts/quick-test.txt --log-level minimal
```

### 2. Functional Tests
**Purpose**: Test core features comprehensively
**File**: `test-prompts/basic-prompts.txt`
**Duration**: ~10-15 minutes
**Use Case**: Regular regression testing

```bash
npm run test:integration-live -- -f test-prompts/basic-prompts.txt
```

### 3. Validation Tests
**Purpose**: Verify response quality and structure
**File**: `test-prompts/structured-prompts.json`
**Duration**: ~8-12 minutes
**Use Case**: Quality assurance and LLM validation

### 4. Fast Delay Analysis Tests
**Purpose**: Analyze delay patterns without waiting for delivery
**Duration**: ~2-5 minutes (vs 10-30 minutes with traditional polling)
**Use Case**: Quick validation of delay timing and message ordering

```bash
# Fast analysis - captures delay info without waiting
npm run test:integration-live -- -f test-prompts/quick-test.txt --skip-delay-wait

# Traditional method - waits for actual message delivery
npm run test:integration-live -- -f test-prompts/quick-test.txt --max-delayed-wait 30
```

```bash
npm run test:integration-live -- -f test-prompts/structured-prompts.json --log-level verbose
```

## Configuration Options

### Command Line Arguments

| Argument | Description | Default | Example |
|----------|-------------|---------|---------|
| `--prompts-file` | Path to test prompts | Required | `test-prompts/basic-prompts.txt` |
| `--output-file` | Results output path | Auto-generated | `my-results.json` |
| `--server-url` | Server hostname | `localhost` | `test-server.com` |
| `--server-port` | Server port | `3000` | `8080` |
| `--max-delayed-wait` | Max wait for delayed messages (seconds) | `30` | `60` |
| `--log-level` | Logging verbosity | `normal` | `verbose` |
| `--skip-delay-wait` | Skip waiting for delays, capture queue info | `false` | `--skip-delay-wait` |

### Log Levels

- **minimal**: Only test results and errors
- **normal**: Test progress and summaries
- **verbose**: Detailed HTTP requests, responses, and timing

### Delay Analysis Modes

**Traditional Mode (default):**
- Waits for actual message delivery based on configured delays
- Captures real-time message flow and timing
- Longer test duration but validates complete user experience
- Use for end-to-end validation and timing verification

**Fast Analysis Mode (`--skip-delay-wait`):**
- Captures delay information from message queue without waiting
- Analyzes planned message timing and ordering
- Significantly faster test execution (5-10x speedup)
- Use for rapid development feedback and delay pattern analysis

```bash
# Fast analysis - captures delay info without waiting (~2-5 minutes)
npm run test:integration-live -- -f test-prompts/quick-test.txt --skip-delay-wait

# Traditional method - waits for actual message delivery (~10-30 minutes)
npm run test:integration-live -- -f test-prompts/quick-test.txt --max-delayed-wait 30
```

## Output and Reports

### JSON Results File
Contains complete test data:
```json
{
  "sessionId": "uuid-here",
  "timestamp": "2025-01-06T12:00:00.000Z",
  "summary": {
    "total": 15,
    "successful": 14,
    "failed": 1,
    "totalDuration": 45000
  },
  "results": [...]
}
```

### Markdown Report
Human-readable summary:
```markdown
# Integration Test Report
Generated: 2025-01-06T12:00:00.000Z

## Summary
- Total Tests: 15
- Successful: 14 ✅
- Failed: 1 ❌
- Success Rate: 93.3%
```

## Best Practices

### 1. Environment Setup
- Use a dedicated test database
- Ensure LLM service quotas are sufficient
- Run tests during off-peak hours
- Monitor system resources

### 2. Test Design
- Start with quick tests before comprehensive suites
- Use structured prompts for validation
- Include edge cases and error scenarios
- Test different conversation themes

### 3. CI/CD Integration
```yaml
# Example GitHub Actions workflow
- name: Run Integration Tests
  run: |
    npm run migrate
    npm run test:integration-live -- -f test-prompts/basic-prompts.txt --log-level minimal
```

### 4. Monitoring and Alerting
- Set up alerts for test failures
- Monitor response times and success rates
- Track LLM usage and costs
- Log test results for trend analysis

## Troubleshooting

### Common Issues

#### Server Startup Failures
```bash
# Check database connection
npm run migrate

# Verify environment variables
cat .env

# Check port availability
lsof -i :3000
```

#### Test Timeouts
```bash
# Increase wait time
npm run test:integration-live -- -f prompts.txt --max-delayed-wait 60

# Check LLM service status
# Verify network connectivity
```

#### Inconsistent Results
```bash
# Run with verbose logging
npm run test:integration-live -- -f prompts.txt --log-level verbose

# Check for rate limiting
# Verify database state
```

### Debug Mode
Enable detailed logging to diagnose issues:
```bash
npm run test:integration-live -- -f test-prompts/quick-test.txt --log-level verbose
```

This shows:
- HTTP request/response details
- Server health check results
- Message polling activity
- Detailed timing information
- Error stack traces

## Performance Benchmarks

### Expected Performance
- **Initial Response**: < 5 seconds
- **Delayed Messages**: 2-15 seconds after initial response
- **Total Test Duration**: ~30-60 seconds per prompt
- **Success Rate**: > 95% under normal conditions

### Performance Monitoring
```bash
# Run performance-focused test
npm run test:integration-live -- -f test-prompts/basic-prompts.txt --log-level verbose | grep "Duration:"
```

## Extending the Test Suite

### Adding New Prompts
1. **Text Format**: Add lines to existing `.txt` files
2. **JSON Format**: Add objects to `prompts` array in `.json` files
3. **New Categories**: Create new prompt files for specific themes

### Custom Test Scenarios
```bash
# Create custom prompt file
echo "My custom test prompt" > my-prompts.txt

# Run custom test
npm run test:integration-live -- -f my-prompts.txt
```

### Advanced Configuration
Modify `IntegrationTestRunner` class for:
- Custom validation logic
- Additional API endpoints
- Different polling strategies
- Enhanced reporting

## Security Considerations

- **API Keys**: Ensure test environment uses separate API keys
- **Database**: Use isolated test database
- **Logging**: Avoid logging sensitive information
- **Cleanup**: Ensure proper cleanup of test data

## Maintenance

### Regular Tasks
- Update test prompts based on new features
- Review and update expected themes/skills
- Monitor test execution times
- Clean up old test result files

### Monitoring Health
- Track test success rates over time
- Monitor LLM response quality
- Check for performance degradation
- Validate against production metrics
