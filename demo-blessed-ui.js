#!/usr/bin/env node

// Demo script to show the blessed UI working without server interference
const blessed = require('blessed');

// Create screen
const screen = blessed.screen({
  smartCSR: true,
  title: 'ForaChat REPL Demo'
});

// Create chat display area (scrollable)
const chatBox = blessed.box({
  parent: screen,
  top: 0,
  left: 0,
  width: '100%',
  height: '100%-3',
  content: '',
  tags: true,
  scrollable: true,
  alwaysScroll: true,
  scrollbar: {
    ch: ' ',
    track: {
      bg: 'cyan'
    },
    style: {
      inverse: true
    }
  },
  border: {
    type: 'line'
  },
  style: {
    fg: 'white',
    bg: 'black',
    border: {
      fg: '#f0f0f0'
    }
  }
});

// Create input area at bottom
const inputBox = blessed.textbox({
  parent: screen,
  bottom: 0,
  left: 0,
  width: '100%',
  height: 3,
  inputOnFocus: true,
  border: {
    type: 'line'
  },
  style: {
    fg: 'white',
    bg: 'black',
    border: {
      fg: '#f0f0f0'
    }
  },
  label: ' Input (Press Enter to send, Ctrl+C to exit) '
});

function addMessageToChat(character, message, color = 'white') {
  const timestamp = new Date().toLocaleTimeString();
  const formattedMessage = `{${color}-fg}[${timestamp}] ${character}: ${message}{/}`;
  
  // Add message to chat box
  const currentContent = chatBox.getContent();
  const newContent = currentContent + (currentContent ? '\n' : '') + formattedMessage;
  chatBox.setContent(newContent);
  
  // Auto-scroll to bottom
  chatBox.setScrollPerc(100);
  screen.render();
}

// Handle input submission
inputBox.on('submit', (value) => {
  const input = value.trim();
  
  if (input.toLowerCase() === 'exit' || input.toLowerCase() === 'quit') {
    screen.destroy();
    console.log('\n👋 Thanks for using ForaChat! Have a great day!');
    process.exit(0);
  }
  
  if (input.length === 0) {
    inputBox.clearValue();
    screen.render();
    return;
  }

  // Clear input and add user message to chat
  inputBox.clearValue();
  addMessageToChat('user', input);

  // Simulate responses
  setTimeout(() => {
    addMessageToChat('Fora', 'That\'s a great question! Let me think about that...', 'green');
  }, 1000);
  
  setTimeout(() => {
    addMessageToChat('Jan', 'I agree with Fora. Here\'s what the data shows...', 'blue');
  }, 2500);
  
  setTimeout(() => {
    addMessageToChat('Lou', 'Both great points! Here\'s my take on it...', 'magenta');
  }, 4000);
  
  screen.render();
});

// Handle Ctrl+C to exit
screen.key(['C-c'], () => {
  screen.destroy();
  console.log('\n👋 Thanks for using ForaChat! Have a great day!');
  process.exit(0);
});

// Handle screen resize
screen.on('resize', () => {
  screen.render();
});

// Initialize demo
console.clear();
addMessageToChat('system', '🚀 ForaChat REPL Demo started!', 'green');
addMessageToChat('system', 'This demonstrates the blessed UI with fixed input at bottom', 'cyan');
addMessageToChat('system', 'Type anything and see simulated responses from Fora, Jan, and Lou', 'yellow');
addMessageToChat('system', 'Notice how the input line stays at the bottom!', 'yellow');

// Focus input box and render screen
inputBox.focus();
screen.render();
