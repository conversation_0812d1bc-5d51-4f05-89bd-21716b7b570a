exports.up = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .alterTable('message_queue', (table) => {
        // Track validation status and decisions
        table.enum('validation_status', ['NOT_VALIDATED', 'VALIDATING', 'VALIDATED']).defaultTo('NOT_VALIDATED').notNullable();
        table.enum('validation_decision', ['SEND_AS_IS', 'WITHDRAW', 'REVISE']).nullable();
        
        // Store original message text before any validation changes
        table.text('original_text').nullable();
        
        // Track validation attempts and timing
        table.integer('validation_attempts').defaultTo(0).notNullable();
        table.timestamp('validation_started_at').nullable();
        table.timestamp('validation_completed_at').nullable();
        
        // Store validation reasoning for debugging
        table.text('validation_reasoning').nullable();
        
        // Add indexes for validation queries
        table.index(['validation_status'], 'idx_queue_validation_status');
        table.index(['validation_decision'], 'idx_queue_validation_decision');
    });
};

exports.down = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .alterTable('message_queue', (table) => {
        table.dropIndex(['validation_status'], 'idx_queue_validation_status');
        table.dropIndex(['validation_decision'], 'idx_queue_validation_decision');
        
        table.dropColumn('validation_status');
        table.dropColumn('validation_decision');
        table.dropColumn('original_text');
        table.dropColumn('validation_attempts');
        table.dropColumn('validation_started_at');
        table.dropColumn('validation_completed_at');
        table.dropColumn('validation_reasoning');
    });
};
