exports.up = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .createTable('message_queue', (table) => {
        table.increments('id').primary();
        table.integer('conversation_id').unsigned().references('id').inTable('forachat.conversations').notNullable();
        table.string('character', 255).notNullable();
        table.text('text').notNullable();
        table.integer('delay_ms').defaultTo(3000).notNullable();
        table.enum('status', ['PENDING', 'PROCESSING', 'VALIDATING', 'SENT', 'CANCELLED', 'WITHDRAWN']).defaultTo('PENDING').notNullable();
        table.integer('priority').defaultTo(100).notNullable(); // Lower number = higher priority
        table.string('similarity_hash', 64).nullable(); // For quick duplicate detection
        table.decimal('similarity_score', 5, 3).nullable(); // For similarity comparison
        table.timestamp('scheduled_at').nullable(); // When the message should be sent
        table.timestamps(true, true);
        
        // Indexes for performance
        table.index(['conversation_id', 'status', 'priority'], 'idx_queue_conversation_status_priority');
        table.index(['conversation_id', 'character', 'status'], 'idx_queue_conversation_character_status');
        table.index(['similarity_hash'], 'idx_queue_similarity_hash');
        table.index(['scheduled_at'], 'idx_queue_scheduled_at');
    });
};

exports.down = function(knex) {
    return knex.schema
    .withSchema('forachat')
    .dropTable('message_queue');
};
