/**
 * Example demonstrating how to use the central Logger utility
 * throughout the ForaChat application
 */

import { DBOS } from '@dbos-inc/dbos-sdk';
import { logger } from '../src/utils/Logger';

// Example service class showing logger usage
export class ExampleService {
  
  @DBOS.step()
  static async processData(data: any): Promise<any> {
    // Log the start of processing
    logger.info('Starting data processing', { dataSize: data.length });
    
    try {
      // Simulate some processing
      const result = await ExampleService.performComplexOperation(data);
      
      // Log successful completion
      logger.info('Data processing completed successfully', { 
        resultCount: result.length,
        processingTime: '150ms' 
      });
      
      return result;
      
    } catch (error) {
      // Log errors with full context
      logger.error('Data processing failed', error, { 
        dataSize: data.length,
        step: 'complex-operation' 
      });
      throw error;
    }
  }
  
  @DBOS.step()
  static async performComplexOperation(data: any): Promise<any> {
    // Log debug information (only visible in debug mode)
    logger.debug('Performing complex operation', { 
      algorithm: 'advanced-processing',
      inputType: typeof data 
    });
    
    // Simulate potential warning condition
    if (data.length > 1000) {
      logger.warn('Processing large dataset', { 
        size: data.length,
        recommendation: 'Consider batch processing' 
      });
    }
    
    // Simulate processing
    return data.map((item: any) => ({ ...item, processed: true }));
  }
  
  @DBOS.workflow()
  static async dataWorkflow(inputData: any): Promise<any> {
    logger.info('=== DATA WORKFLOW STARTED ===');
    logger.info('Workflow parameters', { 
      workflowId: 'data-processing-001',
      inputSize: inputData.length 
    });
    
    try {
      const result = await ExampleService.processData(inputData);
      
      logger.info('=== DATA WORKFLOW COMPLETED ===');
      return result;
      
    } catch (error) {
      logger.error('=== DATA WORKFLOW FAILED ===', error);
      throw error;
    }
  }
}

// Example of using logger in different scenarios
export class LoggerExamples {
  
  // Basic logging
  static basicLogging(): void {
    logger.info('Application started');
    logger.warn('Configuration value missing, using default');
    logger.error('Connection failed');
    logger.debug('Detailed debugging information');
  }
  
  // Logging with context data
  static contextualLogging(): void {
    const user = { id: 123, name: 'John Doe' };
    const action = 'login';
    
    logger.info('User action performed', { 
      userId: user.id, 
      userName: user.name, 
      action: action,
      timestamp: new Date().toISOString()
    });
  }
  
  // Error logging with different error types
  static errorLogging(): void {
    // Standard Error object
    const standardError = new Error('Something went wrong');
    logger.error('Standard error occurred', standardError);
    
    // Custom error with additional context
    try {
      throw new Error('Database connection timeout');
    } catch (error) {
      logger.error('Database operation failed', error, {
        operation: 'user-lookup',
        retryAttempt: 3,
        maxRetries: 5
      });
    }
    
    // String error
    logger.error('Simple error message');
    
    // Error with object context
    logger.error('API request failed', new Error('HTTP 500'), {
      endpoint: '/api/users',
      method: 'GET',
      responseTime: '5000ms'
    });
  }
  
  // Structured logging for monitoring
  static monitoringLogs(): void {
    // Performance monitoring
    logger.info('=== PERFORMANCE METRICS ===');
    logger.info('Request processed', {
      endpoint: '/api/chat',
      method: 'POST',
      responseTime: 250,
      statusCode: 200,
      userId: 'user-123'
    });
    
    // Business metrics
    logger.info('=== BUSINESS METRICS ===');
    logger.info('Conversation completed', {
      conversationId: 'conv-456',
      messageCount: 15,
      duration: '5m 30s',
      characters: ['fora', 'sage', 'echo']
    });
    
    // System health
    logger.info('=== SYSTEM HEALTH ===');
    logger.info('Database connection status', {
      status: 'healthy',
      connectionPool: {
        active: 5,
        idle: 10,
        total: 15
      },
      responseTime: '15ms'
    });
  }
  
  // Migration example: Before and After
  static migrationExample(): void {
    // BEFORE (using DBOS.logger directly)
    // DBOS.logger.info('Processing user request');
    // DBOS.logger.error(`Error: ${(error as Error).message}`);
    
    // AFTER (using central logger)
    logger.info('Processing user request');
    
    const error = new Error('Sample error');
    logger.error('Operation failed', error);
    
    // The central logger automatically handles:
    // - Mode switching (DBOS.logger vs file logging)
    // - Error formatting and stack traces
    // - Consistent timestamp formatting
    // - Graceful fallbacks
  }
}

// Example usage in different modes
export class ModeExamples {
  
  static demonstrateNormalMode(): void {
    // When logger.initialize(false) is called:
    // - All logs go to DBOS.logger
    // - Logs appear in console/DBOS logging system
    // - Structured for DBOS telemetry
    
    logger.info('This goes to DBOS.logger');
    logger.error('This error goes to DBOS.logger', new Error('Sample'));
  }
  
  static demonstrateQuietMode(): void {
    // When logger.initialize(true) is called:
    // - All logs go to server.log file
    // - Console output is suppressed
    // - Formatted with timestamps and levels
    
    logger.info('This goes to server.log');
    logger.error('This error goes to server.log', new Error('Sample'));
  }
  
  static checkLoggerMode(): void {
    if (logger.isQuietMode()) {
      console.log(`Logs are being written to: ${logger.getLogFilePath()}`);
    } else {
      console.log('Logs are being sent to DBOS.logger');
    }
  }
}

// Example of proper cleanup
export class CleanupExample {
  
  static async applicationShutdown(): Promise<void> {
    logger.info('Application shutting down...');
    
    // Perform cleanup operations
    await this.cleanupResources();
    
    // Close logger (this is done automatically in ForaChatApp.shutdown())
    logger.close();
    
    logger.info('Application shutdown complete');
  }
  
  private static async cleanupResources(): Promise<void> {
    logger.debug('Cleaning up application resources');
    // Cleanup logic here
  }
}
